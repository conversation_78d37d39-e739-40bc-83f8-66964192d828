(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_train-pages-training-faceValid"],{"34e7":function(a,n,t){"use strict";t.r(n);var e=t("a0a0"),i=t.n(e);for(var o in e)["default"].indexOf(o)<0&&function(a){t.d(n,a,(function(){return e[a]}))}(o);n["default"]=i.a},"5d1a":function(a,n,t){"use strict";t.r(n);var e=t("c5f7"),i=t("34e7");for(var o in i)["default"].indexOf(o)<0&&function(a){t.d(n,a,(function(){return i[a]}))}(o);var r=t("f0c5"),c=Object(r["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=c.exports},a0a0:function(a,n,t){"use strict";(function(a){t("7a82");var e=t("4ea4").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,t("99af");var i=e(t("c7eb")),o=e(t("1da1")),r=e(t("5b8c")),c={data:function(){return{option:{},hasImage:!1}},onLoad:function(a){this.option=a},methods:{faceValid:function(){try{var n=this;uni.chooseMedia({count:1,sizeType:["original"],mediaType:["image"],sourceType:["camera"],camera:"front",success:function(t){var e=t.tempFiles[0].tempFilePath;n.hasImage=!0,n.option.companyId instanceof Array&&(n.option.companyId=n.option.companyId[0]),uni.uploadFile({url:r.default.apiServer+"app/user/ExmaVerifyImage",filePath:e,name:"file",formData:{EnterpriseID:n.option.companyId,imageUserId:n.option._id,personalTrainingId:n.option.personalTrainingId},fail:function(n){a("log","上传文件错误",n," at pages_train/pages/training/faceValid.vue:47")},success:function(){var a=(0,o.default)((0,i.default)().mark((function a(t){var e;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e=JSON.parse(t.data).data,200===t.statusCode&&(e?(uni.showToast({title:"验证成功，开始考试",duration:800}),uni.navigateTo({url:"/pages_train/pages/training/test?personalTrainingId=".concat(n.option.personalTrainingId,"&companyId=").concat(n.option.companyId,"&id=").concat(n.option._id,"&bigTest=1")})):uni.showModal({confirmText:"确认",title:"验证失败",content:"重新拍照，再次进行验证",confirmColor:"#3B8BFF",cancelColor:"#222222",success:function(a){a.confirm?n.faceValid():a.cancel&&uni.navigateBack()}}));case 2:case"end":return a.stop()}}),a)})));return function(n){return a.apply(this,arguments)}}()})},fail:function(a){},complete:function(a){}})}catch(t){}}}};n.default=c}).call(this,t("0de9")["log"])},c5f7:function(a,n,t){"use strict";t.d(n,"b",(function(){return i})),t.d(n,"c",(function(){return o})),t.d(n,"a",(function(){return e}));var e={gracePage:t("c14d").default},i=function(){var a=this,n=a.$createElement,t=a._self._c||n;return t("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[t("my-header",{attrs:{slot:"gHeader",title:"验证"},slot:"gHeader"}),t("v-uni-view",{staticStyle:{height:"200px",width:"auto",margin:"auto"},attrs:{slot:"gBody"},slot:"gBody"},[t("v-uni-button",{on:{click:function(n){arguments[0]=n=a.$handleEvent(n),a.faceValid.apply(void 0,arguments)}}},[a._v("人员验证")])],1)],1)},o=[]}}]);