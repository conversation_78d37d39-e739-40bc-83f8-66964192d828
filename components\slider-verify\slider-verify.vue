<template>
  <view class="sliderVerify-shade" v-if="show">
    <view class="sliderVerify-wrap" :class="{'loading': loading}">
      <view class="header-actions">
        <view class="refresh-btn" @click="refresh">
          <view class="refresh-icon">↻</view>
        </view>
        <view class="close-btn" @click="handleClose">×</view>
      </view>
      <view class="imgWrap">
        <image class="img" :src="src"></image>
        <view
          class="over"
          :style="{ left: left + 'px', top: top + 'px' }"
        ></view>
        <view
          class="smartImg"
          :style="{ left: sleft + 'px', top: stop + 'px' }"
        >
          <image
            class="simg"
            :style="{ left: -left + 'px', top: -top + 'px' }"
            :src="src"
          ></image>
        </view>
      </view>
      <view class="sliderBox" @touchend="sliderEnd">
        <movable-area class="sliderF">
          <movable-view
            :animation="true"
            class="sliderS"
            :x="sliderx"
            direction="horizontal"
            @change="startMove"
            :style="{ background: primaryColor }"
          >
            <view class="slider-arrow">
              <text class="arrow-right">→</text>
            </view>
          </movable-view>
        </movable-area>
        <view class="bgC">
          <text>{{ sliderText }}</text>
          <view class="bgC_left" :style="{ width: backLeft + 'px', backgroundColor: primaryColor }"></view>
        </view>
      </view>
      <view class="message" v-if="message">{{ message }}</view>
      <view class="loading-mask" v-if="loading">
        <text>{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'slider-verify',
  props: {
    // 显示控制
    show: {
      type: Boolean,
      default: false,
    },
    // 验证码配置
    captchaConfig: {
      type: Object,
      default: () => ({})
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 滑块图标URL
    sliderIconUrl: {
      type: String,
      default: require('@/static/slider.png')
    },
    // 主题色
    primaryColor: {
      type: String,
      default: '#eb4d4d'
    },
    // 成功色
    successColor: {
      type: String,
      default: '#52c41a'
    },
    // 背景色
    bgColor: {
      type: String,
      default: '#e6e6e6'
    },
    // 误差容忍度
    tolerance: {
      type: Number,
      default: 4
    },
    // 文本自定义
    refreshText: {
      type: String,
      default: '刷新'
    },
    sliderText: {
      type: String,
      default: '拖动滑块完成整块拼图'
    },
    loadingText: {
      type: String,
      default: '验证中...'
    },
    successText: {
      type: String,
      default: '验证成功!'
    },
    failText: {
      type: String,
      default: '验证失败，请重试'
    }
  },
  watch: {
    show() {
      if (this.show) {
        // 当显示验证码时，请求新的验证码
        this.$emit('getNewCaptcha');
      }
    },
    captchaConfig: {
      handler(newVal) {
        if (newVal && newVal.captchaImageUrl) {
          // 更新图片源
          this.src = newVal.captchaImageUrl;
          
          // 使用后端返回的位置参数
          if (newVal.captchaTop !== undefined) {
            this.top = this.stop = newVal.captchaTop;
          }
          
          if (newVal.captchaLeft !== undefined) {
            this.left = newVal.captchaLeft;
          }
          
          // 初始化滑块位置
          this.initSliderPosition();
        }
      },
      deep: true
    }
  },
  data() {
    return {
      left: 0, // 拼图目标位置，从后端获取
      top: 0,  // 拼图垂直位置，从后端获取
      sleft: 0, // 滑块当前水平位置
      sleftDefault: 0, // 滑块初始水平位置
      stop: 0,  // 滑块垂直位置，与top保持一致
      sliderx: 0, // 用于滑块动画
      backLeft: 0, // 滑动条背景宽度
      message: '',
      src: '',
      refreshStatus: false,
    };
  },
  mounted() {
    // 组件挂载时请求验证码
    this.$emit('getNewCaptcha');
  },
  methods: {
    // 初始化滑块位置
    initSliderPosition() {
      // 初始化滑块位置，固定在左侧
      this.sleft = this.sleftDefault = 10;
      
      // 重置滑块控制状态
      this.sliderx = 1;
      setTimeout(() => {
        this.sliderx = 0;
        this.refreshStatus = false;
      }, 300);
      
      // 清除消息
      this.message = '';
    },
    
    // 刷新验证码
    refresh() {
      if (this.refreshStatus) return;
      this.refreshStatus = true;
      this.message = '';
      this.$emit('getNewCaptcha');
    },
    
    // 滑动处理
    startMove(e) {
      this.backLeft = e.detail.x + 18;
      this.sleft = this.sleftDefault + e.detail.x;
    },
    
    // 滑动结束处理
    sliderEnd() {
      // 如果正在加载中，不处理
      if (this.loading) return;
      
      // 发送验证请求到父组件，由父组件决定是否验证成功
      // 不在本地判断验证结果，完全依赖后端验证
      this.$emit('verifyCaptcha', {
        captchaId: this.captchaConfig.captchaId,
        moveX: this.sleft
      });
      
      // 不在此处进行本地验证，完全依赖setVerifyResult方法处理结果
      // 移除本地验证逻辑，防止重复调用getNewCaptcha
    },
    
    // 设置验证结果 - 供父组件调用
    setVerifyResult(result) {
      if (result.success) {
        this.message = this.successText;
        // 可以设置样式为成功状态
        this.primaryColor = this.successColor;
      } else {
        this.message = result.message || this.failText;
        
        // 1秒后重置并获取新的验证码
        setTimeout(() => {
          this.message = '';
          // 重新获取验证码
          this.$emit('getNewCaptcha');
          // 重置滑块位置
          this.initSliderPosition();
        }, 1000);
      }
    },
    
    // 重置验证码 - 供父组件调用
    resetCaptcha() {
      this.$emit('getNewCaptcha');
    },

    handleClose() {
      this.$emit('close');
    }
  },
};
</script>

<style lang="scss" scoped>
.sliderVerify-shade {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
}
.sliderVerify-wrap {
  position: relative;
  background: #fff;
  width: 300px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  
  &.loading {
    pointer-events: none;
    opacity: 0.8;
  }
  
  .header-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 6px;
    z-index: 10;
    
    .refresh-btn, .close-btn {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      background-color: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      backdrop-filter: blur(8px);
      transition: all 0.25s ease;
      color: rgba(255, 255, 255, 0.85);
      font-size: 14px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.25);
        color: #ffffff;
        border-color: rgba(255, 255, 255, 0.2);
      }
      
      &:active {
        transform: scale(0.92);
      }
    }

    .refresh-icon {
      transition: transform 0.3s ease;
      font-weight: 300;
    }
    
    .refresh-btn:hover .refresh-icon {
      transform: rotate(180deg);
    }
    
    .close-btn {
      font-size: 18px;
      line-height: 1;
      font-weight: 200;
    }
  }
  .imgWrap {
    position: relative;
    width: 280px;
    height: 150px;
    border-radius: 8px;
    margin: 0 auto;
    overflow: hidden;
    background: #f7f7f7;
    margin-top: 15px;
    .img {
      display: block;
      width: 100%;
      height: 100%;
    }

    .over {
      position: absolute;
      left: 0;
      top: 0;
      width: 40px;
      height: 40px;
      background: rgba(220, 220, 220, 0.8);
      box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.2);
    }

    .smartImg {
      position: absolute;
      z-index: 2;
      left: 0;
      top: 0;
      width: 40px;
      height: 40px;
      overflow: hidden;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);

      .simg {
        position: absolute;
        display: block;
        width: 280px;
        height: 150px;
      }
    }
  }
}

.sliderBox {
  width: 280px;
  margin: 20px auto;
  height: 40px;
  position: relative;
  .sliderF {
    width: 100%;
    height: 100%;
    z-index: 3;
    .sliderS {
      height: 40px;
      width: 40px;
      border-radius: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: all 0.2s;
      
      .slider-arrow {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        font-weight: bold;
        color: white;
        
        .arrow-right {
          font-size: 18px;
        }
      }
      
      &:active {
        transform: scale(1.05);
      }
    }
  }

  .bgC {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 40px;
    border-radius: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #666666;
    background-color: #f5f5f5;
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
    text-align: center;
    overflow: hidden;
  }

  .bgC_left {
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 40px;
    border-top-left-radius: 40px;
    border-bottom-left-radius: 40px;
    line-height: 40px;
    font-size: 14px;
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
    text-align: center;
    opacity: 0.8;
  }
}

.message {
  text-align: center;
  font-size: 14px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 500;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #333;
  backdrop-filter: blur(3px);
}
</style>