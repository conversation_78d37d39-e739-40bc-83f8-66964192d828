(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-history"],{"0ff9":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"uni-page-body[data-v-113b8402]{background:#f6f6f6}body.?%PAGE?%[data-v-113b8402]{background:#f6f6f6}.main[data-v-113b8402]{padding:10px 20px;margin-top:70px;background:#f6f6f6}.main .content[data-v-113b8402]{background:#fff;border-radius:8px;color:#909399;margin-bottom:%?40?%}.main .content .info[data-v-113b8402]{padding:20px 20px 0}.main .content .info uni-view[data-v-113b8402]{margin-bottom:10px}.bottom[data-v-113b8402]{position:fixed;bottom:0;padding:10px 20px;width:100%;box-sizing:border-box}.bottom .btn[data-v-113b8402]{width:100%}.bottom .info[data-v-113b8402]{color:#2b85e4;padding:10px 0}",""]),t.exports=e},"1c1f":function(t,e,n){var a=n("0ff9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=n("4f06").default;o("42dcdb1b",a,!0,{sourceMap:!1,shadowMode:!1})},"7e9a":function(t,e,n){"use strict";n.r(e);var a=n("8bdc"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},"8bdc":function(t,e,n){"use strict";(function(t){n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("5530")),i=a(n("c7eb")),u=a(n("1da1")),r=a(n("9551")),c=a(n("bcd3")),s=a(n("c397")),f=n("26cb"),l=a(n("5b8c")),d={components:{Notification:c.default,SignPanel:r.default},onLoad:function(t){var e=this;return(0,u.default)((0,i.default)().mark((function n(){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.id=t.id,n.next=3,e.getData();case 3:case"end":return n.stop()}}),n)})))()},created:function(){var e=this;return(0,u.default)((0,i.default)().mark((function n(){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.config=l.default,t("log",l.default,"config"," at pages_user/pages/user/history.vue:58");case 2:case"end":return n.stop()}}),n)})))()},mounted:function(){var e=this;return(0,u.default)((0,i.default)().mark((function n(){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:t("log",e.userInfo,"userinfo"," at pages_user/pages/user/history.vue:62");case 1:case"end":return n.stop()}}),n)})))()},data:function(){return{id:"",reorientationInfo:[],show:!1,showSignPanel:!1,config:{},popupvalue:{}}},computed:(0,o.default)({},(0,f.mapGetters)(["userInfo"])),methods:{lookDetail:function(e){t("log",11," at pages_user/pages/user/history.vue:79"),this.show=!0,this.popupvalue=e},close:function(){this.show=!1,this.popupvalue={}},rightClick:function(){},getData:function(){var e=this;return(0,u.default)((0,i.default)().mark((function n(){var a;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,s.default.getAllReorientationInfo({employeeId:e.userInfo._id});case 2:a=n.sent,t("log",33333333,a," at pages_user/pages/user/history.vue:94"),a.data&&(e.reorientationInfo=a.data);case 5:case"end":return n.stop()}}),n)})))()},confirmSign:function(){this.showSignPanel=!0},closeSignPanel:function(){this.showSignPanel=!1}}};e.default=d}).call(this,n("0de9")["log"])},e1e5:function(t,e,n){"use strict";n.r(e);var a=n("f0cac"),o=n("7e9a");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("fa64");var u=n("f0c5"),r=Object(u["a"])(o["default"],a["b"],a["c"],!1,null,"113b8402",null,!1,a["a"],void 0);e["default"]=r.exports},f0cac:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return a}));var a={uNavbar:n("0f78").default,uCellGroup:n("5cf7").default,uCell:n("2453").default,uPopup:n("b82b").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-navbar",{attrs:{title:"历年转岗",autoBack:!0},on:{rightClick:function(e){arguments[0]=e=t.$handleEvent(e),t.rightClick.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"main"},t._l(t.reorientationInfo,(function(e,a){return n("v-uni-view",{key:e.id,staticClass:"content"},[n("v-uni-view",{staticClass:"info"},[n("v-uni-view",[t._v("姓名："+t._s(e.name))]),n("v-uni-view",[t._v("内容："+t._s(e.content))]),n("v-uni-view",[t._v("转岗时间："+t._s(e.times))]),n("v-uni-view",[t._v("备注："+t._s(e.remarks))])],1),n("u-cell-group",{staticStyle:{"margin-top":"20px"}},[n("u-cell",{attrs:{title:"职业病危害告知书",isLink:!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.lookDetail(e)}}})],1)],1)})),1),n("u-popup",{attrs:{mode:"bottom",show:t.show},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}},[n("Notification",{attrs:{officialSeal:t.config.imgUrl+t.popupvalue.officialSeal,hazardNotifications:t.popupvalue.harmFactors},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}})],1),n("u-popup",{attrs:{catchtouchmove:"true",overlayStyle:{"touch-action":"none"},mode:"bottom",show:t.showSignPanel},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSignPanel.apply(void 0,arguments)}}},[n("SignPanel",{attrs:{EnterpriseID:t.popupvalue.EnterpriseID,reorientationInfo:t.popupvalue,reorientationId:t.popupvalue.id,detailId:t.popupvalue.detailId},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSignPanel.apply(void 0,arguments)}}})],1)],1)},i=[]},fa64:function(t,e,n){"use strict";var a=n("1c1f"),o=n.n(a);o.a}}]);