<template>
	<gracePage headerBG="#fff" statusBarBG="#fff" :bounding="false">
		<!-- 页面头部 -->
		<view slot="gHeader">
			<view class="grace-header-body">
				<view class="grace-header-content">
					<view style="width:100%;padding-left:26rpx;" class="grace-h5 grace-bold">职业卫生培训</view>
				</view>
				<!-- <text class="grace-header-icons grace-icons icon-search grace-blue"></text> -->
			</view>
		</view>
		<!-- 页面主体 -->
		<view slot="gBody">
			<!-- 轮播图 start -->
			<swiper class="grace-swiper swiper3" autoplay="true" indicator-color="rgba(255, 255, 255, 1)" 
			indicator-active-color="#4d65f3" interval="3000">
				<swiper-item class="grace-swiper-item" v-for="(item, index) in swiperItems" :key="index">
					<view url='' class="grace-img-in" @tap="newstap(item, categories.trainingEducation.name||'培训教育')">
						<image :src="item.sImg|imgPath" class="grace-swiper-image" mode="aspectFill" @error="toDefaultImg($event, index, 'swiperItems', 'sImg')"></image>
						<view class="grace-swiper-title" v-if="item.title">{{item.title}}</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="grace-common-line"></view>
			<!-- 导航 -->
			<!-- <view class="grace-grids grace-bg-white">
				<navigator :url="item.path" class="grace-grids-items" v-for="(item, index) in trainingPower" :key="index">
					<text class="grace-grids-icon grace-icons" :class="item.icon"></text>
					<text class="grace-grids-text">{{item.title}}</text>
				</navigator>
			</view>
			<view class="grace-common-line"></view> -->
			
			<view class="grace-body grace-bg-white">
				<view>
					<view class="common-line"></view>
					<!-- 培训 -->
					<view v-if="hasLogin">
						<view v-if="isManager">
							<view class="grace-title" @click="toUrl('/pages_train/pages/training/myTraining')">
								<view class="grace-title-border"></view>
								<text class="grace-title-text grace-black">管理员培训</text>
								<view :data-cateid="1" >
									<text class="grace-text-small grace-gray">{{trainingList1.length ? '更多' : '暂无'}}</text>
									<text class="grace-text-small grace-gray grace-icons icon-arrow-right icon-left-margin"></text>
								</view>
							</view>
							<view class="grace-news-list grace-border-b">
								<view class="grace-news-item " v-for="item in trainingList1" :key="item._id" @click="toDetail(1, item)">
									<image :src="(item.firstCourseDetail?item.firstCourseDetail.cover:'')|imgPath" class="grace-news-img grace-news-img-l"></image>
									<view class="grace-news-body">
										<text class="grace-news-title">{{item.name}}</text>
										<text class="grace-news-desc">{{item.Introduction || item.introduction}}</text>
										<view class="grace-news-info">
											<view  class="grace-nowrap">
												<text class="grace-icons grace-news-info-text">&#xe609; {{item.firstCourseDetail?item.firstCourseDetail.views:0}}</text>
												<text class="grace-icons grace-news-info-text" style="margin-left:30rpx;">&#xe6b8; {{item.firstCourseDetail?item.firstCourseDetail.commentLength:0}}</text>
											</view>
											<text class="grace-icons grace-news-info-text">&#xe634; {{item.firstCourseDetail?item.firstCourseDetail.likes:0}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view v-if="EnterpriseID">
						<view class="grace-title"   @click="toUrl('/pages_train/pages/training/myTraining', 3)">
							<view class="grace-title-border"></view>
							<text class="grace-title-text grace-black">员工培训</text>
							<view :data-cateid="1" >
								<text class="grace-text-small grace-gray">{{trainingList3.length ? '更多' : '暂无'}}</text>
								<text class="grace-text-small grace-gray grace-icons icon-arrow-right icon-left-margin"></text>
							</view>
						</view>
						</view>
						<view class="grace-news-list grace-border-b">
							<view class="grace-news-item " v-for="item in trainingList3" :key="item._id" @click="toDetail(3, item)">
								<image :src="(item.firstCourseDetail?item.firstCourseDetail.cover:'')|imgPath" class="grace-news-img grace-news-img-l"></image>
								<view class="grace-news-body">
									<text class="grace-news-title">{{item.name}}</text>
									<text class="grace-news-desc">{{item.Introduction || item.introduction}}</text>
									<view class="grace-news-info">
										<view  class="grace-nowrap">
											<text class="grace-icons grace-news-info-text">&#xe609; {{item.firstCourseDetail?item.firstCourseDetail.views:0}}</text>
											<text class="grace-icons grace-news-info-text" style="margin-left:30rpx;">&#xe6b8; {{item.firstCourseDetail?item.firstCourseDetail.commentLength:0}}</text>
										</view>
										<text class="grace-icons grace-news-info-text">&#xe634; {{item.firstCourseDetail?item.firstCourseDetail.likes:0}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 公开课 -->
					<view class="grace-title" @click="toMorePublicCourses">
						<view class="grace-title-border"></view>
						<text class="grace-title-text grace-black">{{myCoursesList.length?'我的课程':'课程市场'}}</text>
						<view :data-cateid="1" >
							<text class="grace-text-small grace-gray">{{hasLogin?'更多':'查看'}}</text>
							<text class="grace-text-small grace-gray grace-icons icon-arrow-right icon-left-margin"></text>
						</view>
					</view>
					<!-- 我的公开课 -->
					<view class="grace-news-list " v-if="myCoursesList.length">
						<view class="grace-news-item" v-for="course in myCoursesList" :key="course._id" @click="toDetail(2, course)">
							<image :src="course.firstCourseDetail.cover|imgPath" v-if="course.firstCourseDetail" class="grace-news-img grace-news-img-l"
							alt="https://kb.jkqy.net/static/upload/images/20200214/1581674424742504114.jpg"></image>
							<view class="grace-news-body" v-if="course.adminTrainingId">
								<text class="grace-news-title">{{course.adminTrainingId.name}}</text>
								<text class="grace-news-desc">{{course.adminTrainingId.Introduction || course.adminTrainingId.introduction}}</text>
								<view class="grace-news-info">
									<view  class="grace-nowrap">
										<text class="grace-icons grace-news-info-text">&#xe609; {{course.firstCourseDetail?course.firstCourseDetail.views:0}}</text>
										<text class="grace-icons grace-news-info-text" style="margin-left:30rpx;">&#xe6b8; {{course.firstCourseDetail?course.firstCourseDetail.commentLength:0}}</text>
									</view>
									<text class="grace-icons grace-news-info-text">&#xe634; {{course.firstCourseDetail?course.firstCourseDetail.likes:0}}</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 所有的公开课 -->
					<view class="grace-news-list " v-else>
						<view class="grace-news-item" v-for="course in coursesList" :key="course._id" 
						@click="hasLogin?toUrl('/pages_train/pages/training/publicCourseDetail?adminTrainingId='+course._id+'&personalTrainingId='+course.personalTrainingId+'&personalTrainingStatus='+course.personalTrainingStatus):toUrl('/pages/login/login')">
							<image :src="course.firstCourseDetail.cover|imgPath" v-if="course.firstCourseDetail" class="grace-news-img grace-news-img-l"
							alt="https://kb.jkqy.net/static/upload/images/20200214/1581674424742504114.jpg"></image>
							<view class="grace-news-body">
								<text class="grace-news-title">{{course.name}}</text>
								<text class="grace-news-desc">{{course.Introduction || course.introduction}}</text>
								<view class="grace-news-info">
									<view  class="grace-nowrap">
										<text class="grace-icons grace-news-info-text">&#xe609; {{course.firstCourseDetail?course.firstCourseDetail.views:0}}</text>
										<text class="grace-icons grace-news-info-text" style="margin-left:30rpx;">&#xe6b8; {{course.firstCourseDetail?course.firstCourseDetail.commentLength:0}}</text>
									</view>
									<text class="grace-icons grace-news-info-text">&#xe634; {{course.firstCourseDetail?course.firstCourseDetail.likes:0}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
			</view>
			<!-- 占位视图 用于避开底部遮盖 -->
			<view style="height:140rpx;"></view>
		</view>
	</gracePage>
</template>
<script>
import { mapGetters,mapActions } from 'vuex';
import { imgPath,randomDefaultImg } from '@/common.js';
import trainingApi from "@/api/training.js";
import learningApi from "@/api/learning.js";
export default {
	data(){
		return {
			swiperItems: [],
			top: 0,
			EnterpriseID:'',
			pageInfo: {
				total: 0,
				size: 3,
				pageCurrent: 1
			},
			trainingList1: [], // 管理员培训
			trainingList3: [], // 员工培训
			coursesList: [], // 所有公开课
			myCoursesList: [], // 我的公开课
		}
	},
	computed: {
		...mapGetters([
			'trainingPower',
			'userInfo',
			'hasLogin',
			'categories',
			'isManager'
		]),
	},
	
	mounted() {
		this.getSwiperItems();
		// console.log('培训权限：', this.trainingPower);
		console.log('用户信息：', this.userInfo);
		if(this.userInfo.companyId && this.userInfo.companyStatus == 2) {
			this.EnterpriseID = this.userInfo.companyId[this.userInfo.companyId.length - 1] || '';
			console.log('当前绑定的企业id：', this.EnterpriseID)
		}
		if(this.hasLogin){
			this.loginVerification()
			this.getCoursesList();
			if(this.EnterpriseID) this.getTrainingList();
		}else{
			console.log('还未登录或者刷新之后就拿不到用户信息了!');
			this.getAllCoursesList();
		}
	},
	methods:{
		...mapActions(["loginVerification"]), // 登录校验
		toDefaultImg(e, index, objName, key) {
			this[objName][index][key] = randomDefaultImg(this.defaultImgList);
		},
		// 点击更多公开课
		toMorePublicCourses(){
			let url = '/pages/login/login';
			if(this.hasLogin){
				url = this.myCoursesList.length ?'/pages_train/pages/training/myCourses' : '/pages_train/pages/training/publicCourses';
			}
			this.toUrl(url);
		},
		// 点击某个新闻
		newstap(item={}, title=''){
			// 更新数据池中的当前考试一级考试结果和统计
			item.categorieName = title;
			this.$store.commit("setCurArticle", item);
			uni.navigateTo({url:"/pages_learning/pages/learning/artInfo"});
		},
		// 获取轮播图信息
		getSwiperItems(){
			this.categories.trainingEducation && learningApi.list({
				size: 3,
				pageCurrent: 1,
				categories: this.categories.trainingEducation.id, // 培训教育
				isTop: 1,
			}).then(res => {
				console.log('轮播图信息：', this.categories.trainingEducation, res.data);
				this.swiperItems = res.data;
				
			})
		},
		// 跳转到培训详情页
		async toDetail(type, item) {
			if(type == 2){ // 我的公开课
				uni.navigateTo({ url: '/pages_train/pages/training/detail?personalTrainingId='+item._id });
			}else{ // 管理员培训或者员工培训
				let personalTrainingId = item.personalTrainingId;
				if(item.personalTrainingStatus == 0){ // 还没创建个人培训
					personalTrainingId = await this.createPersonalTraining(item._id, item.coursesID, item.electives, +type);
				}
				personalTrainingId && uni.navigateTo({ url: '/pages_train/pages/training/detail?personalTrainingId='+personalTrainingId });
			}
		},
		// 培训列表
		async getTrainingList(){
			if(!this.EnterpriseID){
				uni.showToast({
					title: 'EnterpriseID找不到',
					icon: "none"
				});
				return;
			} 
			if(this.userInfo.companyStatus != 2){
				uni.showToast({
					title: '企业还未成功绑定',
					icon: "none"
				});
				return;
			} 
			if(this.isManager){
				await trainingApi.adminTrainingList({ // 管理员培训
					...this.pageInfo,
					EnterpriseID: this.EnterpriseID,
					trainingType: 1,
					employeesId: this.userInfo.employeeId,
				}).then(res => {
					if(res.data){
						this.trainingList1 = res.data.res || [];
						console.log('管理员培训列表数据：', res.data);
					}
				})
			}
			await trainingApi.employeesTrainingList({ // 员工培训
				...this.pageInfo,
				EnterpriseID: this.EnterpriseID,
				employeesId: this.userInfo.employeeId,
				// completedEmployees: {$elemMatch: {$en: this.userInfo.employeeId} },
			}).then(res => {
				if(res.data){
					this.trainingList3 = res.data.res || [];
					console.log('员工列表数据: ', res.data);
				}
			})
		},
		// 获取个人公开课列表
		getCoursesList(){
			if(!this.userInfo._id){
				uni.showToast({
					title: '登录之后才能学习哦~',
					icon: "none"
				});
				return;
			}
			trainingApi.personalTrainingList({
				...this.pageInfo,
				userId: this.userInfo._id,
				trainingType: 2,
				completeState: false,
			}).then(res => {
				console.log('个人公开课列表：', res.data);
				this.myCoursesList = res.data.res || [];
				if(this.myCoursesList.length == 0) this.getAllCoursesList();
			})
		},
		// 获取所有公开课列表
		getAllCoursesList(){
			learningApi.publicCoursesList({
                ...this.pageInfo,
				trainingType: 2,
			}).then(res => {
				console.log('公开课列表：', res);
				this.coursesList = res.data.res || [];
			})
		},
		async createPersonalTraining(trainingId, coursesID, electives, trainingType){
			if(this.EnterpriseID){
				const param = {
					EnterpriseID: this.EnterpriseID,
					employeesId: this.userInfo.employeeId,
					courses: coursesID.map(ele => {
						return { coursesId: ele, courseType: 1, };
					}),
					adminUserId: this.userInfo.adminUserId || '',
					trainingType,
				}
				if(electives && electives.length){ // 选修课
					param.courses.push(...electives.map(ele => {
						return {
							coursesId: ele,
							courseType: 2,
						};
					}));
				}
				if(trainingType == 1){ // 管理员培训
					if(!this.userInfo.adminUserId){
						uni.showToast({ 
							title: 'adminUserId获取不到，请联系客服', 
							icon : "none"
						}); 
						return;
					}
					param.adminTrainingId = trainingId;
					param.roles = this.userInfo.myRoles || [];
				}else{ // 员工培训
					param.employeesTrainingPlanId = trainingId;
				}
				const res = await trainingApi.createPersonalTraining(param);
				// console.log('创建PersonalTraining：', res);
				if(res.status == 200 && res.data._id) {
					return res.data._id;
				}else{
					uni.showToast({ title: res.data || '个人培训记录添加失败', icon : "none"}); 
				}
			}else{
				console.log('刷新之后就拿不到用户信息了!')
			}
		},
		// 跳转到某个页面
		toUrl(url, trainingType) { 
			if(trainingType){
				uni.navigateTo({ url: url + '?trainingType=' + trainingType })
			}else{
				uni.navigateTo({ url })
			}
		 },
	},
	filters: {
		imgPath: img => imgPath(img),
	},
}
</script>
<style>
.logo{width: 60rpx; height: 60rpx;margin:0 20rpx;}
.grace-swiper{height:300rpx;}
.grace-swiper-item{height:280rpx;}
.swiper-image{width:700rpx; height:280rpx;}
.grace-swiper.swiper3{height:360rpx;}
/* 九宫格个性修饰 */
.grace-grids .grace-icons{width:80rpx; height:80rpx; border-radius: 8rpx;}
.grace-grids .grace-icons.icon-courses{color: #8dc63f;}
.grace-grids .grace-icons.icon-certificate{color: #f37b1d ;}
.grace-grids .grace-icons.icon-exercise{color: #6739b6;}
.grace-grids .grace-icons.icon-examination{color: #008CFF;}
.grace-grids-items{width:25%; padding:25rpx 0; box-sizing:border-box;}
.grace-grids-image{width:80rpx; height:80rpx;}
/* 列表修饰 */
/* 请根据项目来规划自己的图片大小，请对图片进行等比缩放 */
.grace-news-list{margin-top: 0;padding-bottom: 30rpx;}
.grace-title{padding-top: 30rpx;}
.grace-news-item{padding: 20rpx 0;}
.grace-news-img{width:220rpx; height:150rpx;border-radius: 6rpx;}
.big-img{width:700rpx; height:388rpx;  overflow:hidden; margin:15rpx 0;}
.big-image{width:700rpx; height:388rpx; border-radius:6rpx;}
.grace-news-imgs-img{width:225rpx;}
</style>