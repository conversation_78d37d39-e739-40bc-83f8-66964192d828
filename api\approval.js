"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let approvalApi = {
    // 获取申请审批列表
    getApplicationProducts: (data) => {
        return req({
            url: 'manage/adminuser/getDefendproductsAuditList',
            method: 'post',
            data, // current, pageSize, isSelected, year, query
        });
    },
    
    // 审批操作（通过/驳回）
    selectApplication: (data) => {
        return req({
            url: 'manage/adminuser/selectApplication',
            method: 'post',
            data, // _id, status, auditLevel, reason, employeeInfo, products
        });
    },
    
    // // 获取防护用品列表
    // getDefendProductList: (data) => {
    //     return req({
    //         url: 'manage/protectionProduct/getDefendProductList',
    //         method: 'get',
    //         data,
    //     });
    // },
    
    // // 获取申请详情
    // getApplicationDetail: (data) => {
    //     return req({
    //         url: 'manage/protectionApplication/getDetail',
    //         method: 'get',
    //         data, // id
    //     });
    // },
    
    // // 获取审批历史
    // getAuditHistory: (data) => {
    //     return req({
    //         url: 'manage/protectionApplication/getAuditHistory',
    //         method: 'get',
    //         data, // applicationId
    //     });
    // },
    
    // // 批量审批
    // batchAudit: (data) => {
    //     return req({
    //         url: 'manage/protectionApplication/batchAudit',
    //         method: 'post',
    //         data, // ids, status, auditLevel, reason
    //     });
    // },
    
    // // 导出审批记录
    // exportAuditRecords: (data) => {
    //     return req({
    //         url: 'manage/protectionApplication/export',
    //         method: 'post',
    //         data, // yearNumber, mouthNumber, query
    //     });
    // },
    
    // // 获取防护用品规格列表
    // getProductSpecs: (data) => {
    //     return req({
    //         url: 'manage/protectionProduct/getSpecs',
    //         method: 'get',
    //         data, // productId
    //     });
    // },
    
    // // 获取当前用户的审批权限
    // getAuditPermission: (data) => {
    //     return req({
    //         url: 'manage/user/getAuditPermission',
    //         method: 'get',
    //         data,
    //     });
    // },
    
    // // 获取申请统计数据
    // getApplicationStatistics: (data) => {
    //     return req({
    //         url: 'manage/protectionApplication/getStatistics',
    //         method: 'get',
    //         data, // year, month
    //     });
    // }
};

export default approvalApi;
