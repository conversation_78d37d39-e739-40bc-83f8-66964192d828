<template>
	<gracePage :isSwitchPage="true" :isLoading="pageLoading" loadingBG="rgba(255,255,255,1)">
		<view slot="gHeader" style="padding:5rpx 25rpx;">
			<graceNavBar :items="tabs" :currentIndex="currentIndex" @change="navChange" textAlign="center" :isCenter="true" 
			:size="0" activeFontSize="30rpx" lineHeight="70rpx" activeColor="#3688FF" padding="30rpx" activeLineWidth="100%" ></graceNavBar>
		</view>
		<!-- 页面主体 -->
		<view slot="gBody" id="gBody">
			<swiper :style="{height:mainHeight+'px'}" :current="currentIndex" @change="swiperChange">
				<swiper-item v-for="(news, newsIndex) in newsAll" :key="newsIndex">
					<scroll-view scroll-y="true" :style="{height:mainHeight+'px'}" @scroll="scroll" 
					@scrolltolower="scrollend" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend">
						<view style="padding:10rpx 0;">
							<graceReload ref="graceReload" width="700rpx" :reloadBgColor="['', '', '#3688FF']" 
							:reloadColor="['#999999', '#3688FF', '#FFFFFF']" 
							marginLeft="25rpx" @reload="reload"></graceReload>
							<articleList :articles="news" :categoriesTitle="tabs[currentIndex]" :key="currentIndex"></articleList>
							<graceLoading :loadingType="loadingTypes[newsIndex]"></graceLoading>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
	</gracePage>
</template>
<script>
// var demoData = require('@/graceUI/demoData/article.js');
// var demoNews = demoData.articleList;
import learningApi from "@/api/learning.js";
import {randomDefaultImg } from "@/common.js";
import articleList from "@/components/ArticleList.vue";
import { mapGetters } from 'vuex'
export default {
	components: {
		articleList
	},
	data() {
		return {
			mainHeight   : 650, // 列表区域高度
			// 分类数据
			tabsAll      : [],
			tabs         : [],
			currentIndex : 0,
			newsAll      : [], // 新闻信息保存数组
			pages        : [], // 每个选项卡对应的分页
			loadingTypes : [], // 加载状态
			scrollTops   : [], // 每个滚动区域的滚动值
			scrollTimer  : null,
			pageLoading  : false,
			size: 10, // 每页获取数据条数

			
		}
	},
	computed: {
		...mapGetters([
			'windowHeight',
			'categories',
		]),
	},
	onReady:function(){
		// uni.createSelectorQuery().select('#gBody').fields(
		// 	{size: true}, (res) => {
		// 		this.mainHeight = res.height;
		// 		this.pageLoading = false;
		// 	}
		// ).exec();
	},
	watch:{
		currentIndex : function (nVal, oldVal) {
			if(this.newsAll[this.currentIndex].length > 0){return ;}
			if((this.loadingTypes[this.currentIndex] == 3)){ this.getNews(); }
		}
	},
	created() {
		if(this.categories) this.tabsAll = this.categories.health || [];
		this.mainHeight = (this.windowHeight - 220)/2;
		// 初始化新闻列表数组 元素数量与分类匹配
		for(var i = 0; i < this.tabsAll.length; i++){
			this.newsAll.push([]);
			this.tabs.push(this.tabsAll[i].name);
			this.pages.push(1);
			this.loadingTypes.push(3);
			this.scrollTops.push(0);
		}
		// 第一次加载新闻列表
		this.getNews();
	},
	methods: {
		toDefaultImg(e, index, objName, key) {
		this[objName][index][key] = randomDefaultImg(this.defaultImgList);
		},
		// 导航切换
		navChange: function (e) {
			this.currentIndex = e;
		},
		swiperChange: function (e) {
			var index = e.detail.current;
			this.currentIndex = index;	
		},
		getNews : function(isReload){
			// 当前正在展示的 选项index 为 this.currentIndex
			// 那么分类 id 应该为 this.tabsAll[this.currentIndex].id
			//console.log('类型 : ' + this.tabs[this.currentIndex] + ' 第'+ this.pages[this.currentIndex] +'页');
			// 使用内部变量记录 当前索引，避免加载时用户切换引起的变量变化
			var currentIndex = this.currentIndex;
			this.loadingTypes.splice(currentIndex, 1, 1);
			learningApi.list({
				size: this.size,
				pageCurrent: this.pages[this.currentIndex],
				categories: this.tabsAll[this.currentIndex].id, // 文章分类，对应的是contentcategories表id
			}).then(res => {
				console.log(this.tabsAll[this.currentIndex].name+'列表数据：', res.data);
				if(isReload){
					setTimeout(()=>{
						this.$refs.graceReload[currentIndex].endReload();
					},300)
				}
				// 此处我们模拟第三页加载全部，实际情况请参考 
				// http://grace.hcoder.net/manual/info/139-30.html
				if(this.pages[currentIndex] >= 3){
					this.loadingTypes.splice(currentIndex, 1, 2);
					return ;
				}
				// 填充新闻数据
				if(this.pages[currentIndex] == 1){
					this.newsAll.splice(currentIndex, 1 ,  res.data);
					// this.newsAll.splice(currentIndex, 1 , demoNews);
					// console.log(7777777, this.newsAll)
					
				}else{
					this.newsAll.splice(currentIndex,1, this.newsAll[currentIndex].concat(res.data));
					// this.newsAll.splice(currentIndex,1, this.newsAll[currentIndex].concat(demoNews));
					// console.log(8888888888, this.newsAll)
				}
				
				// 页码增加
				this.pages[currentIndex]++;
				this.loadingTypes.splice(currentIndex, 1, 3);
			})
			
		},
		// 滚动加载更多
		scrollend : function () {
			if(this.loadingTypes[this.currentIndex] != 3){ return false; }
			console.log('loadmore.....');
			this.getNews();
		},
		// 滚动记录
		scroll : function (e) {
			if(this.scrollTimer != null){clearTimeout(this.scrollTimer);}
			this.scrollTimer = setTimeout(()=>{
				this.scrollTops.splice(this.currentIndex, 1, e.detail.scrollTop);
			}, 100);
		},
		// 手势数据传递
		touchstart : function (e){
			var touchObj = {scrollTop : this.scrollTops[this.currentIndex], moveY : e.changedTouches[0].pageY};
			this.$refs.graceReload[this.currentIndex].touchstart(touchObj);
		},
		touchmove : function (e) {
			var touchObj = {scrollTop : this.scrollTops[this.currentIndex], moveY : e.changedTouches[0].pageY};
			this.$refs.graceReload[this.currentIndex].touchmove(touchObj);
		},
		touchend : function (e) {
			var touchObj = {scrollTop : this.scrollTops[this.currentIndex], moveY : e.changedTouches[0].pageY};
			this.$refs.graceReload[this.currentIndex].touchend(touchObj);
		},
		// 下拉刷新
		reload:function(){
			this.pages[this.currentIndex] = 1;
			this.loadingTypes.splice(this.currentIndex, 1, 3);
			this.getNews(1);
		},
		
	}
}
</script>
<style scoped>
.grace-news-header{background-color:#FFFFFF; height:100rpx; padding:0 25rpx;}
uni-view{font-size: 12px!important;}
</style>
