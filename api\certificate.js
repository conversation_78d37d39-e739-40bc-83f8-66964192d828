"use strict";
import req from '@/utils/http.js' //导入 封装的请求

export default {
  // 获取培训证书的批次和编号
  getCount: param => {
    return req({
      url: 'manage/adminTraining/getCertificateCount',
      method: 'get',
      param,
    });
  },
  add: data => {
    return req({
      url: 'manage/adminTraining/addCertificate',
      method: 'post',
      data,
    });
  },
  purchase: data => {
    return req({
      url: 'manage/adminTraining/purchaseCertificate',
      method: 'post',
      data,
    });
  },
  get: data => {
    return req({
      url: 'manage/adminTraining/getCertificate',
      method: 'post',
      data,
    });
  },
  getMyInfo: data => {
    return req({
      url: 'manage/adminTraining/getMyInfo',
      method: 'post',
      data,
    });
  },
  wxSpPay: data => {
    return req({
      url: 'app/pay/wxSpPay',
      method: 'post',
      data,
    });
  },
	// 调起培训支付
	trainNativePay: (data) => {
		return req({
			url: 'app/trainPay/trainNativePay',
			method: 'post',
			data,
		})
	},
	// 使用团购码
	useCdk: (data) => {
		return req({
			url: 'manage/adminTraining/useCdk',
			method: 'post',
			data,
		})
	},
};

