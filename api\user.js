"use strict";
import req from '@/utils/http.js' //导入 封装的请求
import config from '@/common.js';

let api = {
  getWorkPlace: (data) => {
    return req({
      url: 'app/user/getWorkPlace',
      method: 'get',
      data,
    });
  },
  confirmCheck: (data) => {
    return req({
      url: 'manage/user/confirmPhysicalExamination',
      method: 'post',
      data,
    });
  },
  getphysicalExaminationNotice: (data) => {
    // 获取体检提醒消息
    return req({
      url: 'manage/user/getphysicalExaminationNotice',
      method: 'post',
      data,
    });
  },

  getListByPower: (data) => {
    return req({
      url: 'manage/user/getListByPower',
      method: 'get',
    });
  },
  // 修改用户名或密码
  loginVerification: (data) => {
    return req({
      url: 'manage/user/loginVerification',
      method: 'get',
      data,
    });
  },
  // 获取验证码
  sendVerificationCode: (data) => {
    //sendVerificationCode 表示方法名,data表示参数
    return req({
      url: 'app/sendVerificationCode', //请求接口
      method: 'get', //请求方法
      data, //传参: 1、phoneNum 2、countryCode:86 3、messageType:类型str 0注册1登录7修改手机号或密码
      // 关于类型，手机登录注册接口用 1 ，用户名密码注册用 0 ，修改手机号或密码用 7
    });
  },
  // 手机验证码登录
  phoneLogin: (data) => {
    return req({
      url: 'app/user/smDoLogin',
      method: 'post',
      data,
    });
  },
  //微信授权登录注册
  authLoginAction: (data) => {
    return req({
      url: 'app/user/authLoginAction',
      method: 'post',
      data,
    });
  },
  // 微信公众号授权登录注册
  wxH5AuthAction: (data) => {
    return req({
      url: 'app/user/wxH5AuthAction',
      method: 'post',
      data,
    });
  },
  // 获取并绑定手机号
  bindPhoneNum: (data) => {
    return req({
      url: 'app/user/bindPhoneNum',
      method: 'post',
      data,
    });
  },
  // 获取并绑定手机号
  bindWxInfo: (data) => {
    return req({
      url: 'manage/user/bindWxInfo',
      method: 'post',
      data,
    });
  },
  //支付宝授权登录注册
  alipayAuthLoginAction: (data) => {
    return req({
      url: 'app/user/alipayAuthLoginAction',
      method: 'post',
      data,
    });
  },
  // 用户名密码登录
  userNameLogin: (data) => {
    return req({
      url: 'app/user/doLogin',
      method: 'post',
      data,
    });
  },
  // 退出登录
  logout: () => {
    return req({
      url: 'manage/user/logOut',
      method: 'get',
    });
  },
  // 修改用户名或密码
  updateUserInfo: (data) => {
    return req({
      url: 'manage/user/updateInfo',
      method: 'put',
      data, // password 密码 phoneNum 手机号 countryCode 国家代码，默认传86 messageCode验证码
    });
  },
  // 用户名注册
  doReg: (data) => {
    return req({
      url: 'app/user/doReg',
      method: 'post',
      data, // password 密码 phoneNum 手机号 countryCode 国家代码，默认传86 messageCode验证码 password confirmPassword
    });
  },
  // 获得所有企业列表
  getCompanyList: (data) => {
    return req({
      url: 'manage/adminorg/getListApp',
      method: 'get',
      data, // cname 企业名称关键字，必传参，且不能为空
    });
  },
  // 根据用户手机号码获取可选企业(针对已经注册或者登陆的用户)
  getCompanysByPhoneNum: (data) => {
    return req({
      url: 'manage/user/getCompanysByPhoneNum',
      method: 'get',
      data, // phoneNum
    });
  },
  // 绑定企业
  boundEnterprise: (data) => {
    return req({
      url: 'manage/user/boundEnterprise',
      method: 'post',
      data, // name companyId idNo
    });
  },
  // 解除绑定当前企业
  unbindEnterprise: (data) => {
    return req({
      url: 'manage/user/unbindEnterprise',
      method: 'get',
      data, // userId
    });
  },
  // 确认体检医院
  confirmSelectHospital: (data) => {
    return req({
      url: 'manage/user/confirmSelectHospital',
      method: 'post',
      data,
    });
  },
  //获取体检信息
  physicalExamination: (data) => {
    return req({
      url: 'manage/user/physicalExamination',
      method: 'post',
      data, // name companyId idNo
    });
  },

  // 删除引导单
  deleteGuideForm: (data) => {
    return req({
      url: 'manage/user/deleteGuideForm',
      method: 'post',
      data, // name companyId idNo
    });
  },
  // 获取体检医院列表createReservation
  findAllReservation: (data) => {
    return req({
      url: 'manage/user/findAllReservation',
      method: 'get',
      data, //
    });
  },
  // 提交预约
  createReservation: (data) => {
    return req({
      url: 'manage/user/createReservation',
      method: 'post',
      data, //
    });
  },
  // 添加留言反馈
  addComments: (data) => {
    return req({
      url: 'manage/comment/add',
      method: 'post',
      data, // 参数：comments 数组
    });
  },
  // 获取留言反馈
  getComments: (data) => {
    return req({
      url: 'manage/comment/get',
      method: 'post',
      data, // 参数：comments 数组
    });
  },
  // 留言上传图片
  uploadCommentImage: (config.apiServer || '') + 'app/user/uploadCommentImage',
  // 查询身份证信息, 也是验证身份证号码
  findInfoByIDNum: (data) => {
    return req({
      url: 'manage/user/findInfoByIDNum',
      method: 'post',
      data, // 参数：IDNum
    });
  },
  // 查询用户危害告知书
  findRecordPDF: (data) => {
    return req({
      url: 'manage/user/findRecordPDF',
      method: 'post',
      data,
    });
  },

  // 保存用户签名
  uploadSignImage: (data) => {
    return req({
      url: 'manage/user/uploadSignImage',
      method: 'post',
      data,
    });
  },

  // 将转岗的一系列通知关闭
  closeNotify: (data) => {
    return req({
      url: 'app/user/closeNotify',
      method: 'post',
      data,
    });
  },
  ppeSign: (data) => {
    return req({
      url: 'manage/user/ppeSign',
      method: 'post',
      data,
    });
  },
  //我的信息上传头像
  uploadHeadImage: (data) => {
    return req({
      url: 'manage/user/uploadLogo',
      method: 'post',
      data,
    });
  },
  //更新我的信息，目前只有身份证
  updateUserInfo: (data) => {
    return req({
      url: 'manage/user/updateUserInfo',
      method: 'post',
      data,
    });
  },
  // 修改用户名或密码
  updateInfo: (data) => {
    return req({
      url: 'manage/user/updateInfo',
      method: 'post',
      data,
    });
  },
  getLastPhysicalDataAndDeteData: (data) => {
    return req({
      url: 'manage/user/getLastPhysicalDataAndDeteData',
      method: 'get',
      data,
    });
  },

  // 获取历年体检趋势
  getIndicatorsTrend: (data) => {
    return req({
      url: 'manage/user/indicatorsTrend',
      method: 'get',
      data,
    });
  },

  // 获取所有指标
  getAllIndicators: (data) => {
    return req({
      url: 'manage/user/getAllIndicators',
      method: 'get',
      data,
    });
  },

  personSign: (data) => {
    return req({
      url: 'app/user/personSign',
      method: 'post',
      data,
    });
  },
  pxVerifyImage: (data) => {
    console.log(data, '摄像头拍摄');
    return req({
      url: 'app/user/pxVerifyImage',
      method: 'post',
      data,
    });
  },
  ExmaVerifyImage: (data) => {
    console.log(data, '考试摄像头拍摄');
    return req({
      url: 'app/user/ExmaVerifyImage',
      method: 'post',
      data,
    });
  },
  // 获取消息通知
  getMessageNotice: (data) => {
    return req({
      url: 'manage/adminuser/getMessageNotice',
      method: 'get',
      data,
    });
  },
  // 获取违章通知
  getViolationNotice: (data) => {
    return req({
      url: 'manage/adminuser/getViolationNotice',
      method: 'get',
      data,
    });
  },
  // 确认消息通知
  confirmMessageNotice: (data) => {
    return req({
      url: 'manage/adminuser/confirmMessageNotice',
      method: 'post',
      data,
    });
  },

  // 获取通知内容
  findNotices: (data) => {
    return req({
      url: 'app/user/findNotices',
      method: 'post',
      data,
    });
  },

  // 获取转岗详细信息
  getReorientationInfo: (data) => {
    return req({
      url: 'app/user/getReorientationInfo',
      method: 'post',
      data,
    });
  },
  // 获取历年转岗详细信息
  getAllReorientationInfo: (data) => {
    return req({
      url: 'app/user/getAllReorientationInfo',
      method: 'post',
      data,
    });
  },
  // 企业微信h5授权登录
  wxWorkAuthAction: (data) => {
    return req({
      url: 'api/wxWorkAuthAction',
      method: 'get',
      data,
    });
  },
  // 获取问卷列表
  getQuestionnaireList: (data) => {
    return req({
      url: 'manage/user/getQuestionnaireList',
      method: 'get',
      data,
    });
  },
  // 获取滑动验证码
  getCaptcha: (data) => {
    return req({
      url: 'app/user/getCaptcha',
      method: 'get',
      data,
    });
  },
  // 验证滑动验证码
  verifyCaptcha: (data) => {
    return req({
      url: 'app/user/verifyCaptcha',
      method: 'post',
      data,
    });
  },
  // 获取系统配置
  getSystemConfig: (data) => {
    return req({
      url: 'api/getSystemConfig',
      method: 'get',
      data,
    });
  },

  // 查询用户胃肠镜的体检记录/查看结果/结果确认
  getCWScopeRecord: (data) => {
    return req({
      url: 'manage/tjAppointment/getCWScopeRecord',
      method: 'get',
      data,
    });
  },

};
export default api;  //导出
