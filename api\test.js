"use strict";
import req from '@/utils/http.js' //导入 封装的请求

export default {
  // 根据题库id获取该题库中的所有题目
  getTopicsRandom: data => {
    return req({
      url: 'manage/testPaper/getTopicsRandom',
      method: 'post',
      data,
    });
  },
  // 添加一次我的考试记录
  addTestRecord: data => {
    return req({
      url: 'manage/adminTraining/addTestRecord',
      method: 'post',
      data,
    });
  },
  
  // getDetail: params => {
  //   return req({
  //     url: 'manage/testPaper/getDetail',
  //     method: 'get',
  //     params,
  //   });
  // },
  // addTestPaper: data => {
  //   return req({
  //     url: 'manage/testPaper/add',
  //     method: 'post',
  //     data,
  //   });
  // },
  // delTestPaper: data => {
  //   return req({
  //     url: 'manage/testPaper/del',
  //     method: 'post',
  //     data,
  //   });
  // },
  // editTestPaper: data => {
  //   return req({
  //     url: 'manage/testPaper/edit',
  //     method: 'post',
  //     data,
  //   });
  // },
  // 获取试卷列表
  // getList: data => {
  //   return req({
  //     url: 'manage/testPaper/getList',
  //     method: 'post',
  //     data,
  //   });
  // },
  // 获取题库数据
  // getQB: data => {
  //   return req({
  //     url: 'manage/questionBank/getQB',
  //     method: 'post',
  //     data,
  //   });
  // },
  // createQB: data => {
  //   return req({
  //     url: 'manage/questionBank/createQB',
  //     method: 'post',
  //     data,
  //   });
  // },
  // delQB: data => {
  //   return req({
  //     url: 'manage/questionBank/delQB',
  //     method: 'post',
  //     data,
  //   });
  // },
  // createTopic: data => {
  //   return req({
  //     url: 'manage/questionBank/createTopic',
  //     method: 'post',
  //     data,
  //   });
  // },
  // addSomeTopic: data => {
  //   return req({
  //     url: 'manage/questionBank/addSomeTopic',
  //     method: 'post',
  //     data,
  //   });
  // },
  // updateTopic: data => {
  //   return req({
  //     url: 'manage/questionBank/updateTopic',
  //     method: 'post',
  //     data,
  //   });
  // },
  // getTopic: data => {
  //   return req({
  //     url: 'manage/questionBank/getTopic',
  //     method: 'post',
  //     data,
  //   });
  // },
  // delTopic: data => {
  //   return req({
  //     url: 'manage/questionBank/delTopic',
  //     method: 'post',
  //     data,
  //   });
  // },
  // findLabel: data => {
  //   return req({
  //     url: 'manage/questionBank/findLabel',
  //     method: 'post',
  //     data,
  //   });
  // },
  // addLabel: data => {
  //   return req({
  //     url: 'manage/questionBank/addLabel',
  //     method: 'post',
  //     data,
  //   });
  // },
  // delLabel: data => {
  //   return req({
  //     url: 'manage/questionBank/delLabel',
  //     method: 'post',
  //     data,
  //   });
  // },
  // updateLabel: data => {
  //   return req({
  //     url: 'manage/questionBank/updateLabel',
  //     method: 'post',
  //     data,
  //   });
  // },
  
};

