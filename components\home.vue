<template>
  <gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
    <!-- 页面头部 -->
    <view slot="gHeader">
      <view class="grace-header-body">
        <!-- <text
          class="grace-header-icons grace-icons icon-scancode grace-white"
          @click="scanCode"
        ></text> -->
        <view class="grace-header-content">
          <view
            style="width: 100%; margin-left: 1rem"
            @click="toUrl('/pages_learning/pages/learning/search')"
          >
            <!-- @click="toUrl('/pages/training/courses/search')"> -->
            <graceSearch placeholder="关键字搜索"></graceSearch>
          </view>
        </view>
        <!-- #ifndef MP-WEIXIN -->
        <text
          class="grace-header-icons grace-icons icon-user grace-white"
          @tap="
            toUrl(
              hasLogin ? '/pages_user/pages/user/info' : '/pages/login/login'
            )
          "
        ></text>
        <!-- #endif -->
      </view>
    </view>
    <!-- 页面主体 -->
    <view slot="gBody">
      <!-- 个人信息概览s -->
      <view class="card grace-box-shadow infoCardBg" style="position: relative; padding: 5px 10px">
        <img src="@/static/homeBG.jpg" style="width: 100%; height: 165px" alt="" />
        <view style="position: absolute; top: 21px; width: 93%">
          <view class="infoCard">
            <text class="nameStyle">{{ userInfo.name }}</text>
            <!-- <text class="sexStyle" v-if="userInfo.gender === '0'">男{{ userInfo.age }}岁</text> -->
            <!-- <text class="sexStyle" v-else>女{{ userInfo.age || "" }}岁</text> -->
            <text class="sexStyle"> {{ userAge }}</text>
            <text class="sexStyle">{{ userGender }}</text>
            <text class="sexStyle">{{ userWorkYears }}</text>
          </view>
          <view class="personInfo infoCard nameColor">{{
            userInfo.company || ""
          }}</view>
          <view class="personInfo infoCard nameColor">{{
            userInfo.workType || ""
          }}</view>
          <view class="personInfo infoCard grace-ellipsis nameColor">职业病危害因素：{{ userInfo.harmFactors || "无" }}
          </view>
          <view class="personInfo infoCard grace-ellipsis nameColor">可能导致的职业病：{{ illnessInfo || "无" }}
          </view>
        </view>
      </view>
      <!-- 轮播图 start -->
      <view class="grace-common-line"></view>
      <!-- 推荐图标 -->
      <graceShade @closeShade="closeShade" ref="graceShade">
        <view class="demo-msg grace-relative" @tap.stop="" style="width: 320px">
          <view v-if="dialog" class="dialog">
            <view v-if="noCheckResult && noPhysicalResult">
              <view class="title">健康建议</view>
              <view class="body" style="text-align: center; height: 60%; line-height: 160px">暂无数据</view>
              <view class="line"></view>
              <view class="closeBtn" @click="closeDailogFun">我知道了</view>
            </view>
            <view v-else>
              <view class="title">健康建议</view>
              <view class="body" style="height: 60%">
                <text class="name">检测结果：</text>
                <text class="result" style="padding: 3px 6px; background-color: #c1ffa8"
                  v-if="checkResultData">未超标</text>
                <view v-if="checkResultData">
                  <text class="name">健康建议：</text>
                  <text class="result">您的岗位数据无异常,请继续保持!</text>
                </view>
                <text class="result" style="padding: 3px 6px; background-color: #ffa8a8"
                  v-if="!noCheckResult && factors.length">
                  超标
                </text>
                <view v-if="!noCheckResult && factors.length">
                  <text class="name">健康建议：</text>
                  <text class="result">
                    您所在的岗位,&nbsp;&nbsp;{{
                      factors.join("、")
                    }}&nbsp;&nbsp;危害因素超出了职业接触限值的要求,请联系管理人员做好工程防护，并正确使用个人防护用品</text>
                </view>
                <text class="result" style="padding: 3px 6px" v-if="noCheckResult">
                  无检测数据
                </text>
                <br />
                <text class="name">体检结果：</text>
                <text v-if="noPhysicalResult" style="padding: 3px 6px" class="result">暂无体检结果</text>
                <text class="result" style="padding: 3px 6px" :style="[styleObject]"
                  v-if="!noPhysicalResult && tjCwithO">
                  {{ tjCwithO }}</text>
                <br />
                <text class="name" v-if="normal === '1'">健康建议：</text>
                <text class="result" style="padding: 3px 6px" v-if="noPhysicalResult && normal === '1'">
                  请联系管理人员进行体检
                </text>
                <text class="result" style="padding: 3px 6px"
                  v-if="!noPhysicalResult && physicalResult && normal === '1'">
                  未见异常
                </text>
                <view v-if="!noPhysicalResult && physicalResult">
                  <text class="name">健康建议：</text>
                  <text class="result">您的岗位体检数据无异常,请继续保持!</text>
                </view>

                <text class="result" style="padding: 3px 6px" v-if="!noPhysicalResult && tjCwithO">
                  <text v-if="tjCwithO === '其他疾病或异常'">
                    您的体检结果为-{{ tjCwithO }},请按照医学建议做好个人健康管理
                  </text>
                  <text v-if="tjCwithO === '复查'">
                    您的体检结果为-{{ tjCwithO }},请按照医学建议做好个人健康管理
                  </text>
                  <text v-if="tjCwithO === '禁忌证'">
                    您的体检结果为-{{
                      tjCwithO
                    }},按要求安排调岗工作，做好个人健康管理。
                  </text>
                  <text v-if="tjCwithO === '疑似职业病'">
                    您的体检结果为-{{
                      tjCwithO
                    }},请联系管理人员进行职业病诊断，做好个人健康管理。
                  </text>
                </text>
              </view>
              <view class="line"></view>
              <view class="closeBtn" @click="closeDailogFun">我知道了</view>
            </view>
          </view>
        </view>
      </graceShade>

      <view style="white-space: nowrap; width: 100%">
        <u-notice-bar v-if="notices && notices.length > 0" style="white-space: nowrap; width: 95%" speed="250"
          direction="column" mode="link" :text="notices.map((e) => e.content)" @click="handleNotice"></u-notice-bar>
      </view>

      <graceShade @closeShade="closeShade2" ref="graceShade2" style="width: 340px" class=".grace-flex-vcenter">
        <view class="demo-msg grace-relative" @tap.stop="" style="width: 340px">
          <view v-if="questionnaireModel" class="dialog">
            <view class="title">问卷调查</view>
            <view class="body" style="
                margin-top: 10rpx;
                text-align: center;
                height: 100rpx;
                color: rgba(136, 136, 136, 1);
              ">
              为了更好的协助企业管理，企业邀请您填写调查问卷</view>
            <view class="line"></view>
            <view class=".grace-flex-center">
              <text class="closeBtn closeKnow" @click="closeDailogFun2">我知道了</text>
              <text class="closeBtn" @click="gotoQuestionnaire">去填写</text>
            </view>
          </view>
        </view>
      </graceShade>
      <view class="grace-grids grace-bg-white">
        <view class="grace-title grace-margin">我的应用</view>
        <view class="grace-grids grace-bg-white" style="margin: 8px 0px; width: 100%">
          <!-- <view class="grace-grids grids-border-wrap" v-if="hasLogin">
					<text class="iconfont icon-MBEfenggeduosetubiao-tixing"></text>
					<view class="grace-grids-items grids-border-item grace-relative" v-for="(item,index) in workPower" :key="item.path" @click="toUrl(hasLogin?`${item.path}`:'/pages/login/login')" v-show="index < 6">
						<text class="grace-grids-icon grace-icons" :class="item.icon"></text>
						<text v-if="item.title === '体检信息'" :class="{badge:physicalExaminationNotice && JSON.stringify(physicalExaminationNotice) !== '{}'}">
						</text>
						<text v-if="item.title === '防护用品领用'" :class="{badge:defendproducts}">
						</text>
						<text class="grace-grids-text grace-list-title-text">{{ item.title }}</text>
					</view>
				</view> -->
          <view class="upView" style="background-color: aliceblue">
            <!--使用方法: click:事件 myFlag:标识  myText:文字 imgSrc:图片地址 size:图片尺寸 -->
            <MenuBtn myFlag="2" @goPath="goPath" :myText="item.title" v-for="(item, index) in workPower"
              :imgSrc="item.icon" :key="index" :path="item.path"></MenuBtn>
          </view>
          <!-- <view
          class=""
          v-for="(item, index) in workPower"
          :key="index"
          style="width: 20%; display: flex; justify-content: center"
        >
          <text
            style="
              text-align: center;
              margin: auto;
              display: flex;
              justify-content: center;
            "
            @click="gotoPage(item.title)"
          >
            <text
              class="grace-grids-icon grace-icons"
              :class="item.icon" style="border-radius: 12%;background: linear-gradient(135deg,#699afe,#699afe);"
            ></text>
            <text class="grace-grids-text middleText">{{ item.title }}</text>
          </text>
        </view> -->
        </view>
      </view>
      <!-- 占位视图 用于避开底部遮盖 -->
      <view style="height: 120rpx"></view>

      <!-- 林卉桐--防护用品领用确认弹框 -->
      <graceDialog closeBtnColor="#999" ref="graceDialog" @closeDialog="closeDialog2">
        <view class="content2" slot="content">
          <text>请填写您要领取的数量</text>
          <br />
          <view style="height: 50px; line-height: 50px; white-space: nowrap">口罩：
            <input class="uni-input" type="number" placeholder="数量" @blur="getPpeNum($event.detail.value, 'quantity_k')"
              style="
                display: inline-block;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 2px 10px;
                margin-right: 8px;
                width: 50%;
              " />&nbsp;&nbsp;个
          </view>
          <br />
          <view>耳塞：<input class="uni-input" type="number" placeholder="数量"
              @blur="getPpeNum($event.detail.value, 'quantity_e')" style="
                display: inline-block;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 2px 10px;
                margin-right: 8px;
                width: 50%;
              " />&nbsp;&nbsp;个</view>
          <!-- <text>{{PPE.info}}</text> -->
          <br />
          <button @tap="createCanvas" type="success" style="
              background-color: #4c84ff;
              border-radius: 4px;
              color: #fff !important;
              margin-left: 15%;
            ">
            签字确认
          </button>
        </view>
      </graceDialog>
      <view class="signature" v-show="showCanvas">
        <canvas class="mycanvas" canvas-id="mycanvas" @touchstart="touchstart" @touchmove="touchmove"
          @touchend="touchend"></canvas>
        <view class="footer">
          <view class="left" @click="finish">保存</view>
          <view class="right" @click="clear">清除</view>
          <view class="close" @click="close">关闭</view>
        </view>
      </view>
    </view>
  </gracePage>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import learningApi from "@/api/learning.js";
import trainingApi from "@/api/training.js";
import { imgPath, randomDefaultImg } from "@/common.js";
import employeeApi from "@/api/employee.js"; //导入接口
import questionnaireApi from "@/api/questionnaire.js"; //导入接口
import graceDialog from "@/graceUI/components/graceDialog.vue";
import config from "@/common.js";
import userApi from "@/api/user.js";
import graceRightMessage from "@/graceUI/components/graceRightMessage.vue";
import MenuBtn from "@/components/MenuBtn.vue";
export default {
  data() {
    return {
      defaultImgList: [
        "/static/upload/enterprise/jnFczjlqoZ/1708889211779236666.png",
      ],
      noticeArr: [],
      questionnaireModel: false,
      msgAimate: true,
      // ctx:{},
      PPE: {
        info: "本柜您可领取",
        data: "", // 需要出货的防护用品
        quantity_k: "", //口罩数量
        quantity_e: "", // 耳塞数量
        codeId: "", // 终端id
      },
      points: [],
      showCanvas: false,
      PPEId: [],
      swiperItems: [], // 轮播图
      indexCate: [
        {
          path: "/pages_user/pages/user/physicalAndCheckResult",
          title: "健康建议",
          icon: "icon-share orange one",
        },
        {
          path: "/pages_user/pages/user/diseasesOverhaul",
          title: "检修维护",
          icon: "icon-phone2 orange one",
        },
        {
          path: "/pages_user/pages/user/physicalExamination",
          title: "体检信息",
          icon: "icon-safe orange one",
        },
        {
          path: "/pages_user/pages/user/info",
          title: "岗位信息",
          icon: "icon-menu two",
        },
        {
          path: "/pages_user/pages/user/ppe",
          title: "防护用品",
          icon: "icon-article four",
        },
        {
          path: "/pages_user/pages/user/comment",
          title: "反馈建议",
          icon: "icon-comments three",
        },
        {
          path: "/pages_user/pages/user/history",
          title: "历年转岗",
          icon: "icon-menu two",
        },
      ],
      industryNews: [], // 行业动态
      myTraining: [], // 在线培训
      top: 0,
      speakerMsgs: [
        // { title: "您2021-03-03的体检结果已经出来的，请及时查收", url:"../index/index", opentype: "navigate"},
        // { title: "试卷已发送，请及时完成", url: "../index/index", opentype: "switchTab"}
      ],
      EnterpriseID: "",
      hasGetNews: false, // 已经获取新闻信息了
      hasTraining: false, // 是否有培训（管理员培训和员工培训）
      dialog: false,
      checkResultData: false, //检测结果未异常
      physicalResult: false, // 体检结果无异常
      noCheckResult: true, //没有检测数据
      noPhysicalResult: true, //没有体检数据
      //体检结果的各种[疑似职业病,复查,禁忌证,其他疾病或异常]
      factors: [], // 超标的因素
      noData: false, // 体检和检测都没有数据

      styleObject: {
        backgroundColor: "#C1FFA8",
      },
      tjCwithO: "",
      normal: "1",
      hasInfo: false,
    };
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "hasLogin",
      "categories",
      "notices",
      "userPower",
      "workPower",
      "harmFactors",
    ]),

    // 性别
    userGender() {
      let gender = {
        '0': '男',
        '1': '女'
      }
      return gender[this.userInfo.gender] || ''

    },
    // 年龄
    userAge() {
      if (this.userInfo.age) {
        return this.userInfo.age + '岁'
      }
      return ''
    },
    // 工龄
    userWorkYears() {
      if (this.userInfo.workYears) {
        return '工龄：' + this.userInfo.workYears
      }
      return ''
    }
  },
  watch: {
    categories(newVal) {
      if (newVal && !this.hasGetNews) {
        this.getSwiperItems(); // 健康企业信息平台 文章
        this.getIndustryNews(); // 行业动态 文章
      }
    },
    userInfo(newVal) {
      if (newVal && !this.hasGetNews) {
        this.hasLogin && this.getTrainingList();
        // this.stationInfo();
      }
    },
    dialog(valold, valnew) {
      if (valold) {
        this.getPhysicalAndCheckResult();
      }
    },
  },
  async mounted() {
    if (this.categories) {
      this.getSwiperItems(); // 健康企业信息平台 文章
      this.getIndustryNews(); // 行业动态 文章
    }
    if (
      this.userInfo.companyStatus == 2 &&
      this.userInfo.companyId &&
      this.userInfo.companyId.length
    ) {
      this.EnterpriseID =
        this.userInfo.companyId[this.userInfo.companyId.length - 1] || "";
    }
    if (this.hasLogin) this.getTrainingList();
    if (this.hasLogin) {
      this.getPhysicalAndCheckResult();
    }
    // 喇叭信息通知
    // if(this.userInfo && this.userInfo.userName){
    // 	// 获取体检信息
    // 	this.physicalExaminationNotice = uni.getStorageSync('physicalExaminationNotice');
    // 	if(this.physicalExaminationNotice){
    // 		if (this.physicalExaminationNotice.type === '将到期') {
    // 			this.noticeContent =`请您尽快进行职业健康检查`
    // 		} else if (this.physicalExaminationNotice.type === '新上岗') {
    // 			this.noticeContent = `感谢你加入${this.userInfo.company}，请您在30日内进行上岗前职业健康检查`
    // 		} else if (this.physicalExaminationNotice.type === '转岗') {
    // 			this.noticeContent = `您已经进行了岗位变动，请于30日内进行职业健康检查`
    // 		} else if (this.physicalExaminationNotice.type === '离岗') {
    // 			this.noticeContent = `你已经申请离职，感谢您为公司的发展作出的贡献，请于30日内进行离岗时职业健康检查`
    // 		} else if (this.physicalExaminationNotice.type === '已过期') {
    // 			this.noticeContent = `您的体检已过期`
    // 		}
    // 		this.speakerMsgs.push({
    // 			title: this.noticeContent,
    // 			url:"/pages_user/pages/user/physicalExamination",
    // 			opentype: "navigate"
    // 		});
    // 	}
    // 	if(!this.userInfo.companyId || !this.userInfo.companyId.length || this.userInfo.companyStatus == 0){
    // 		this.speakerMsgs.push({
    // 			title: '您还未绑定企业，请前往绑定企业',
    // 			url:"/pages_user/pages/user/boundEnterprise",
    // 			opentype: "navigate"
    // 		});
    // 	}
    // }else{
    // 	this.speakerMsgs.push({
    // 		title: '您还未登录，请前往用户登录',
    // 		url:"/pages/login/login",
    // 		opentype: "navigate"
    // 	});
    // }
    const res = await questionnaireApi.getPersonalQuestionnaire({});
    if (res.status === 200) {
      this.questionnaireList = res.data;
      const hasPending = res.data.some((item) => item.isFilled === "待填写");
      // 如果存在待填写的项，将this.questionnaireModel赋值为true
      if (hasPending) {
        console.log(123);
        this.questionnaireModel = true;
        this.$refs.graceShade2.showIt();
      } else {
        this.questionnaireModel = false;
      }
    }
  },
  methods: {
    // 获取岗位信息
    async stationInfo() {
      const userInfo = this.userInfo;
      const stationinfo = await employeeApi.stationInfo(userInfo);
      this.stationinfo = stationinfo.data;
      let harmFactorsList = [];
      if (this.stationinfo && this.stationinfo.length) {
        for (let i = 0; i < this.stationinfo.length; i++) {
          if (!harmFactorsList.includes(this.stationinfo[i])) {
            harmFactorsList.push(this.stationinfo[i].harmFactors);
          }
        }
      }
      this.userInfo.harmFactors = harmFactorsList.join(",");
      console.log(harmFactorsList);

      console.log(this.stationinfo, "岗位信息-------");
      console.log(this.userInfo, "用户信息-------");
    },
    toDefaultImg(e, index, objName, key) {
      this[objName][index][key] = randomDefaultImg(this.defaultImgList);
    },
    handleNotice(index) {
      const target = this.notices[index];
      if (target.type === "reorientation") {
        uni.navigateTo({
          url: `/pages_reorientation/pages/reorientation/reorientation?id=${target.id}&employeeId=${this.userInfo.employeeId}`,
        });
      }
    },
    closeDailog() {
      this.dialog = false;
    },
    async getPhysicalAndCheckResult() {
      let companyID;
      if (this.userInfo.companyId && this.userInfo.companyId.length) {
        companyID = this.userInfo.companyId[0];
      }
      // this.userInfo.companyId = companyID;
      const userInfo = JSON.parse(JSON.stringify(this.userInfo));
      userInfo.companyId = companyID;

      const result = await userApi.getLastPhysicalDataAndDeteData(userInfo);
      let physicalData = result.data.res; //体检结果
      let checkResult = result.data.res2; //检测结果
      if (
        (physicalData && physicalData.length) ||
        (checkResult && checkResult.length)
      ) {
        this.hasInfo = true;
      }
      this.tjCwithO = ""; //体检诊断的病名
      if (physicalData && physicalData.length) {
        let tjTime = physicalData[0].checkDate;
        if (tjTime && new Date(tjTime).getFullYear() === 2023) {
          this.noPhysicalResult = false; //此时有体检结果
          this.tjCwithO = physicalData[0].CwithO;
        } else {
          this.noPhysicalResult = true;
        }
      } else {
        //此时没有体检结果
        this.noPhysicalResult = true;
      }
      if (this.tjCwithO && this.tjCwithO === "疑似职业病") {
        // 页面上不同病名需要有不同的颜色显示
        this.styleObject = {
          backgroundColor: "#FFDAA8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "禁忌证") {
        this.styleObject = {
          backgroundColor: "#FFA8A8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "复查") {
        this.styleObject = {
          backgroundColor: "#FFDAA8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "其他疾病或异常") {
        this.styleObject = {
          backgroundColor: "#FFDAA8",
        };
      }
      if (this.tjCwithO && this.tjCwithO === "目前未见异常") {
        this.physicalResult = true; //体检结果无异常
        this.normal = "2";
        this.styleObject = {
          backgroundColor: "#C1FFA8",
        };
      }
      let arr = [];
      if (checkResult && checkResult.length) {
        for (let i = 0; i < checkResult.length; i++) {
          for (let k = 0; k < checkResult[i].data.length; k++) {
            let time = checkResult[i].data[k].checkTime;
            if (time && new Date(time).getFullYear() === 2023) {
              this.noCheckResult = false; //此时有检测数据
              // if (checkResult[i].data[k].checkResult === "不符合") { 更换判断标准 percent的值 大于1超标 小于等于1不超标
              if (checkResult[i].data[k].percent > 1) {
                this.factors.push(checkResult[i].data[k].checkProject);
              }
            }
            for (let p = 0; p < checkResult[i].data.length; p++) {
              let temp = checkResult[i].data[p];
              if (temp.percent > 1) {
                arr.push(temp);
              }
            }
          }
        }
        setTimeout(() => {
          if (arr.length == 0) {
            this.checkResultData = true;
          }
        }, 1);
        this.factors = Array.from(new Set(this.factors));
      } else {
        this.noCheckResult = true; //此时没有检测数据
      }
    },
    closeShade: function () {
      this.$refs.graceShade.hideIt();
    },
    closeShade2: function () {
      this.$refs.graceShade2.hideIt();
    },
    closeDailogFun() {
      this.dialog = false;
      this.$refs.graceShade.hideIt();
    },
    closeDailogFun2() {
      this.questionnaireModel = false;
      this.$refs.graceShade2.hideIt();
    },
    gotoPage(name) {
      if (name === "反馈建议") {
        uni.navigateTo({ url: "/pages_user/pages/user/comment" });
      }
      if (name === "防护用品") {
        uni.navigateTo({ url: "/pages_user/pages/user/ppe" });
      }
      if (name === "岗位信息") {
        uni.navigateTo({ url: "/pages_user/pages/user/info" });
      }
      if (name === "体检信息") {
        uni.navigateTo({ url: "/pages_user/pages/user/physicalExamination" });
      }
      if (name === "健康建议") {
        this.dialog = true;
        this.$refs.graceShade.showIt();
        // this.$refs.graceShade.showIt();
      }
      if (name === "检修维护") {
        uni.navigateTo({ url: "/pages_user/pages/user/diseasesOverhaul" });
      }
    },
    goPath(item) {
      if (item.name === "健康建议") {
        this.dialog = true;
        this.$refs.graceShade.showIt();
      } else {
        uni.navigateTo({ url: item.path });
      }
    },
    // 扫码
    scanCode() {
      // 临时放在这
      // this.$refs.graceDialog.open();
      // this.PPE.codeId = '863488050210916';
      // console.log('触发扫码', this.userInfo);
      /////--------------------------
      // 微信内嵌浏览器运行H5版时，可通过js sdk实现扫码，需要引入一个单独的js，详见
      // https://uniapp.dcloud.io/api/system/barcode?id=scancode
      // https://ask.dcloud.net.cn/article/35380
      this.PPE.info = "本柜您可领取";
      this.PPE.PPEId = [];
      const userInfo = this.userInfo;
      if (!userInfo._id) {
        uni.showToast({
          title: "您还未登录，请前往用户登录",
          icon: "none",
          duration: 3000,
        });
        setTimeout(function () {
          uni.navigateTo({ url: "/pages/login/login" });
        }, 1000);
      }
      const that = this;
      uni.scanCode({
        scanType: ["barCode", "qrCode"],
        success: async function (res) {
          if (res.result === "863488050210916") {
            that.$refs.graceDialog.open();
            console.log(
              res.result,
              that.PPE.quantity_k,
              that.PPE.quantity_e,
              "机器的id----------"
            );
            const id = res.result;
            that.PPE.codeId = res.result;
          }
          // if (id) {
          // 	let quantity_k = 0;
          // 	let quantity_e = 0;
          // 	const res = await employeeApi.getDefendproducts(userInfo);
          // 	const data = res.data.filter(item =>{
          // 		if (item.pname === '口罩' && !item.acknowledge ) { // 没领取的
          // 			quantity_k += Number(item.quantity);
          // 			that.PPEId.push(item._id);
          // 			return item;
          // 		}
          // else if(item.pname === '耳塞'){
          // 	quantity_e += Number(item.quantity);
          // 	that.PPEId.push(item._id);
          // 	return item; // 记得改成false
          // }
          // })
          // if(data.length>0){
          // 	that.PPE.quantity_k = quantity_k;
          // 	that.PPE.quantity_e = quantity_e;
          // 	that.PPE.data = data;
          // 	if(quantity_k){
          // 		that.PPE.info +=`${quantity_k||'0'}个口罩`
          // 	}else if(quantity_e){
          // 		that.PPE.info +=`${quantity_e||'0'}个耳塞`
          // 	}
          // 	that.$refs.graceDialog.open();
          // }else{
          // 	uni.showToast({
          // 		title: '本柜无您需要领取的防护用品',
          // 		icon:'none',
          // 	})
          // }
          // }else{
          // 	uni.showToast({
          // 		title: res.result,
          // 		icon: "success"
          // 	})
          // }
        },
        fail: function (err) {
          // 在扫码界面点击返回也会进入 fail 回调中
          uni.showToast({
            title: err,
            icon: "none",
          });
        },
      });
    },
    getPpeNum(val, name) {
      if (name === "quantity_k") {
        this.PPE.quantity_k = val;
      } else if (name === "quantity_e") {
        this.PPE.quantity_e = val;
      }
    },
    // async change(index){
    // 	this.showSignature = true;
    // 	console.log(this.$refs.graceDialog)
    // 	this.$refs.graceDialog.open();
    // 	this.id = index;
    // 	},
    closeDialog2: function () {
      this.$refs.graceDialog.hide();
    },
    graceDialog() {
      this.$refs.graceDialog.hide();
    },
    //关闭并清空画布
    close: function () {
      this.showCanvas = false;
      this.clear();
    },
    //创建并显示画布
    createCanvas: function () {
      this.showCanvas = true;
      this.ctx = uni.createCanvasContext("mycanvas", this); //创建绘图对象
      //设置画笔样式
      this.ctx.lineWidth = 4;
      this.ctx.lineCap = "round";
      this.ctx.lineJoin = "round";
    },
    //触摸开始，获取到起点
    touchstart: function (e) {
      let startX = e.changedTouches[0].x;
      let startY = e.changedTouches[0].y;
      let startPoint = { X: startX, Y: startY };
      this.points.push(startPoint);
      //每次触摸开始，开启新的路径
      this.ctx.beginPath();
    },

    //触摸移动，获取到路径点
    touchmove: function (e) {
      let moveX = e.changedTouches[0].x;
      let moveY = e.changedTouches[0].y;
      let movePoint = { X: moveX, Y: moveY };
      this.points.push(movePoint); //存点
      let len = this.points.length;
      if (len >= 2) {
        this.draw(); //绘制路径
      }
    },

    // 触摸结束，将未绘制的点清空防止对后续路径产生干扰
    touchend: function () {
      this.points = [];
    },

    /* ***********************************************
    #   绘制笔迹
    #	1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
    #	2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
    #	3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
    ************************************************ */
    draw: function () {
      let point1 = this.points[0];
      let point2 = this.points[1];
      this.points.shift();
      this.ctx.moveTo(point1.X, point1.Y);
      this.ctx.lineTo(point2.X, point2.Y);
      this.ctx.stroke();
      this.ctx.draw(true);
    },

    //清空画布
    clear: function () {
      let that = this;
      uni.getSystemInfo({
        success: function (res) {
          let canvasw = res.windowWidth;
          let canvash = res.windowHeight;
          that.ctx.clearRect(0, 0, canvasw, canvash);
          that.ctx.draw(true);
        },
      });
    },
    //完成绘画并保存到本地
    finish: function () {
      let that = this;
      uni.canvasToTempFilePath(
        {
          canvasId: "mycanvas",
          fail: (err) => { },
          success: function (res) {
            //上传到服务器
            // const defendproducts = that.defendproducts.filter(item=>{
            // 	if (item._id === that.id) {
            // 		return item;
            // 	}
            // })
            // console.log(defendproducts, '这应该是个对象======');
            // console.log(that.userInfo, '???');
            uni.uploadFile({
              url: config.apiServer + "app/user/ppeSign",
              filePath: res.tempFilePath,
              name: "file",
              // formData:{EnterpriseID:that.path.EnterpriseID,employeeId : that.aboutTransfer.employeeId},
              formData: {
                EnterpriseID: that.userInfo.companyId.length
                  ? that.userInfo.companyId[0]
                  : "",
                id: JSON.stringify(that.PPEId),
                num: JSON.stringify({
                  quantity_k: that.PPE.quantity_k,
                  quantity_e: that.PPE.quantity_e,
                }),
                userId: that.userInfo._id,
              },
              fail: (err) => {
                console.log("上传文件错误", err);
              },
              success: async (uploadFileRes) => {
                console.log(uploadFileRes, "文件上传结果");
                if (uploadFileRes.statusCode === 200) {
                  console.log("个人防护用品签字====");
                  that.clear();
                  that.showCanvas = false;
                  // uni.navigateBack({delta:2})
                  // console.log({data: JSON.stringify(that.PPE.data),id:that.PPE.codeId,num:{quantity_k:that.PPE.quantity_k,quantity_e:that.PPE.quantity_e},companyId:that.userInfo.companyId[0]}, '扫码的结果');
                  const result = await employeeApi.ppeCabinetQRcode({
                    data: JSON.stringify(that.PPE.data),
                    id: that.PPE.codeId,
                    num: {
                      quantity_k: that.PPE.quantity_k,
                      quantity_e: that.PPE.quantity_e,
                    },
                    companyId: that.userInfo.companyId[0],
                  });
                  console.log(result, "????????");
                  if (result.status === 200) {
                    uni.showToast({
                      title: "出货成功",
                      icon: "success",
                    });
                  } else {
                    uni.showToast({
                      title: "出货失败:" + result.message,
                      icon: "warn",
                    });
                  }
                }
              },
            });
            that.$refs.graceDialog.hide();
          },
        },
        this
      );
    },
    // 获取轮播图信息，也就是健康企业信息平台
    getSwiperItems() {
      this.categories.informationPlatform &&
        learningApi
          .list({
            size: 3,
            pageCurrent: 1,
            categories: this.categories.informationPlatform.id, // 健康企业信息平台
            isTop: 1,
          })
          .then((res) => {
            this.hasGetNews = true;
            this.swiperItems = res.data;
          });
    },
    // 获取行业动态
    getIndustryNews() {
      this.categories.industryNews &&
        learningApi
          .list({
            size: 4,
            pageCurrent: 1,
            categories: this.categories.industryNews.id, // 行业动态
          })
          .then((res) => {
            this.industryNews = res.data;
          });
    },
    // 点击某个新闻
    newstap(item, title = "") {
      // 更新数据池中的当前考试一级考试结果和统计
      item.categorieName = title;
      this.$store.commit("setCurArticle", item);
      uni.navigateTo({ url: "/pages_learning/pages/learning/artInfo" });
    },
    // 个人培训列表（personalTrainingList）
    async getTrainingList() {
      if (!this.userInfo._id) return;
      const query = {
        size: 4,
        pageCurrent: 1,
        userId: this.userInfo._id,
        completeState: "false",
        trainingType: 2,
      };
      if (this.EnterpriseID) delete query.trainingType;
      trainingApi.personalTrainingList(query).then((res) => {
        this.myTraining = res.data.res || [];
        this.hasTraining = this.myTraining.some(
          (ele) => ele.trainingType !== 2
        );
      });
    },
    // 跳转到某个页面
    toUrl(url) {
      uni.navigateTo({ url });
    },
    gotoQuestionnaire() {
      uni.navigateTo({ url: "/pages_user/pages/user/questionnaire" });
    },
  },
  filters: {
    imgPath: (img) => imgPath(img),
    showContent(simpleComments) {
      if (JSON.parse(simpleComments)[0].type == "contents") {
        return JSON.parse(simpleComments)[0].content;
      } else {
        return JSON.parse(simpleComments)[1].content || "";
      }
    },
  },
  components: {
    graceDialog,
    graceRightMessage,
    MenuBtn,
  },
  onLoad: function (options) {
    console.log(options, "是否有问卷");
  },
};
</script>
<style lang="scss" scoped>
.grace-img-card-img {
  position: relative;

  .grace-img-card-title {
    position: absolute;
    height: 280rpx;
    width: 86%;
    left: 7%;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    text-align: center;

    .title1 {
      font-size: 24rpx;
    }

    .title2 {
      font-size: 40rpx;
      line-height: 60rpx;
      font-weight: bold;
      text-shadow: 1px 3px 5px #666;
      display: block;
      margin: 15rpx 0 20rpx;
    }

    .title3 {
      display: inline-block;
      line-height: 34rpx;
    }
  }
}

.grace-swiper {
  height: 300rpx;
}

.grace-swiper-item {
  height: 280rpx;
}

.swiper-image {
  width: 700rpx;
  height: 280rpx;
}

.grace-swiper.swiper3 {
  height: 360rpx;
}

/* 九宫格个性修饰 */
.grace-grids .grace-icons {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  color: #fff;
}

.grace-grids .grace-icons.one {
  background-color: #8dc63f;
}

.grace-grids .grace-icons.three {
  background-color: #008cff;
}

.grace-grids .grace-icons.two {
  background-color: #6739b6;
}

.grace-grids .grace-icons.four {
  background-color: #f37b1d;
}

.grace-grids-items {
  width: 20%;
  padding: 25rpx 0;
  box-sizing: border-box;
}

.grace-grids-image {
  width: 80rpx;
  height: 80rpx;
}

.grace-grids-icon {
  font-size: 52rpx;
}

.grace-grids .grace-icons {
  display: flex !important;
  align-items: bottom;
  justify-content: center;
  flex-direction: column;
}

/* 产品列表修饰 */
.grace-img-card-img {
  height: 340rpx;
  background-color: #f5f5f5;
}

.grace-img-card-price {
  margin-right: 20rpx;
}

/* 签字的 */
.webView {
  margin-top: 30px;
}

.signature {
  position: fixed;
  top: 10px;
  left: 2%;
  z-index: 999;
  width: 96%;
}

page {
  background: #fff;
}

.container {
  padding: 20rpx 0 120rpx 0;
  box-sizing: border-box;
}

.title {
  height: 50upx;
  line-height: 50upx;
  font-size: 16px;
}

.middleText {
  color: #636568;
  font-size: 16rpx;
}

.dialog {
  width: 70%;
  border-radius: 8px;
  background-color: white;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  // box-shadow: 0px 0px 12px  rgb(0,0,0,.4);
  padding: 0 20px;
  z-index: 999;
}

.title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  height: 40px;
  line-height: 40px;
  padding-top: 10px;
}

.name {
  font-size: 16px;
  line-height: 30px;
}

.result {
  line-height: 30px;
  font-size: 16px;
}

.closeBtn {
  text-align: center;
  padding: 20px;
  color: #576b95;
}

.line {
  padding-top: 20px;
  height: 1px;
  background-color: none;
  box-shadow: 0 0.5px 0 #eee;
}

.closeKnow {
  color: rgba(136, 136, 136, 1);
}

.infoCardBg {
  background-size: 100% 100%;
  box-shadow: 0px 0px 0px #fff;
}

.nameStyle {
  width: 51px;
  height: 20px;
  font-size: 20px;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  color: #fff;
  line-height: 20px;
  margin-bottom: 15px;
}

.sexStyle {
  width: 39px;
  height: 14px;
  color: #fff;
  font-size: 14px;
  line-height: 14px;
  margin-left: 16px;
}

.personInfo {
  height: 14px;
  font-size: 14px;
  font-weight: 300;
  text-align: left;
  color: #000;
  line-height: 14px;
  margin-top: 15px;
}

.card {
  border-radius: 10px;
  padding: 25px 10px;
}

.infoCard {
  margin-left: 15px;
}

.upView {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-left: 12px;
  width: calc(100vw - 24px);
  margin-top: 10px;
  padding: 4px 0px;
  background-color: white;
}
</style>