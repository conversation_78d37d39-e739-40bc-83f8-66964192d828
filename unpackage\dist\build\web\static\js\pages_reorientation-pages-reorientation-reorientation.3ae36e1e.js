(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_reorientation-pages-reorientation-reorientation"],{"02c6":function(t,e,o){"use strict";(function(t){o("7a82");var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(o("5530")),a=n(o("c7eb")),r=n(o("1da1")),d=n(o("9551")),u=n(o("bcd3")),l=n(o("c397")),s=o("26cb"),c=n(o("5b8c")),f={components:{Notification:u.default,SignPanel:d.default},onLoad:function(t){var e=this;return(0,r.default)((0,a.default)().mark((function o(){return(0,a.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return e.id=t.id,e.employeeId=t.employeeId,o.next=4,e.getData();case 4:case"end":return o.stop()}}),o)})))()},created:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.config=c.default;case 1:case"end":return e.stop()}}),e)})))()},data:function(){return{id:"",employeeId:"",reorientationInfo:{},show:!1,showSignPanel:!1,config:{}}},computed:(0,i.default)({},(0,s.mapGetters)(["userInfo"])),methods:{lookDetail:function(){t("log",11," at pages_reorientation/pages/reorientation/reorientation.vue:77"),this.show=!0},close:function(){this.show=!1},rightClick:function(){},getData:function(){var e=this;return(0,r.default)((0,a.default)().mark((function o(){var n;return(0,a.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,l.default.getReorientationInfo({id:e.id,employeeId:e.employeeId});case 2:n=o.sent,t("log",33333333,n," at pages_reorientation/pages/reorientation/reorientation.vue:91"),n.data&&(e.reorientationInfo=n.data);case 5:case"end":return o.stop()}}),o)})))()},confirmSign:function(){this.showSignPanel=!0},closeSignPanel:function(){this.showSignPanel=!1}}};e.default=f}).call(this,o("0de9")["log"])},"08b4":function(t,e,o){"use strict";var n=o("8e8e"),i=o.n(n);i.a},"1e31":function(t,e,o){"use strict";o("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("a9e3");var n={props:{show:{type:Boolean,default:uni.$u.props.loadingIcon.show},color:{type:String,default:uni.$u.props.loadingIcon.color},textColor:{type:String,default:uni.$u.props.loadingIcon.textColor},vertical:{type:Boolean,default:uni.$u.props.loadingIcon.vertical},mode:{type:String,default:uni.$u.props.loadingIcon.mode},size:{type:[String,Number],default:uni.$u.props.loadingIcon.size},textSize:{type:[String,Number],default:uni.$u.props.loadingIcon.textSize},text:{type:[String,Number],default:uni.$u.props.loadingIcon.text},timingFunction:{type:String,default:uni.$u.props.loadingIcon.timingFunction},duration:{type:[String,Number],default:uni.$u.props.loadingIcon.duration},inactiveColor:{type:String,default:uni.$u.props.loadingIcon.inactiveColor}}};e.default=n},"1fd8":function(t,e,o){"use strict";o.r(e);var n=o("ec5f"),i=o("eee7");for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);o("08b4");var r=o("f0c5"),d=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"4493ad28",null,!1,n["a"],void 0);e["default"]=d.exports},3715:function(t,e,o){"use strict";o.r(e);var n=o("adbe"),i=o("c7fa");for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);o("3cbb");var r=o("f0c5"),d=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"26861ad0",null,!1,n["a"],void 0);e["default"]=d.exports},"3cbb":function(t,e,o){"use strict";var n=o("e514"),i=o.n(n);i.a},"43d3":function(t,e,o){"use strict";o("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}};e.default=n},"48cc":function(t,e,o){var n=o("24fb");e=n(!1),e.push([t.i,"uni-page-body[data-v-4493ad28]{background:#f6f6f6}body.?%PAGE?%[data-v-4493ad28]{background:#f6f6f6}.main[data-v-4493ad28]{padding:10px 20px;margin-top:70px}.main .content[data-v-4493ad28]{background:#fff;border-radius:8px;color:#909399}.main .content .info[data-v-4493ad28]{padding:20px 20px 0}.main .content .info uni-view[data-v-4493ad28]{margin-bottom:10px}.bottom[data-v-4493ad28]{position:fixed;bottom:0;padding:10px 20px;width:100%;box-sizing:border-box}.bottom .btn[data-v-4493ad28]{width:100%}.bottom .info[data-v-4493ad28]{color:#2b85e4;padding:10px 0}",""]),t.exports=e},6072:function(t,e,o){"use strict";o("7a82");var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("a630"),o("3ca3");var i=n(o("1e31")),a={name:"u-loading-icon",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}},computed:{otherBorderColor:function(){var t=uni.$u.colorGradient(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:t:"transparent"}},watch:{show:function(t){}},mounted:function(){this.init()},methods:{init:function(){setTimeout((function(){}),20)},addEventListenerToWebview:function(){var t=this,e=getCurrentPages(),o=e[e.length-1],n=o.$getAppWebview();n.addEventListener("hide",(function(){t.webviewHide=!0})),n.addEventListener("show",(function(){t.webviewHide=!1}))}}};e.default=a},"661e":function(t,e,o){"use strict";o("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{openType:String},methods:{onGetUserInfo:function(t){this.$emit("getuserinfo",t.detail)},onContact:function(t){this.$emit("contact",t.detail)},onGetPhoneNumber:function(t){this.$emit("getphonenumber",t.detail)},onError:function(t){this.$emit("error",t.detail)},onLaunchApp:function(t){this.$emit("launchapp",t.detail)},onOpenSetting:function(t){this.$emit("opensetting",t.detail)}}};e.default=n},6679:function(t,e,o){"use strict";o("7a82");var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("c975");n(o("43d3")),n(o("661e"));var i=n(o("e305e")),a={name:"u-button",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{}},computed:{bemClass:function(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor:function(){return this.plain?this.color?this.color:uni.$u.config.color["u-".concat(this.type)]:"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom:function(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor:function(){var t={};return this.color&&(t.color=this.plain?this.color:"white",this.plain||(t["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(t.borderTopWidth=0,t.borderRightWidth=0,t.borderBottomWidth=0,t.borderLeftWidth=0,this.plain||(t.backgroundImage=this.color)):(t.borderColor=this.color,t.borderWidth="1px",t.borderStyle="solid")),t},nvueTextStyle:function(){var t={};return"info"===this.type&&(t.color="#323233"),this.color&&(t.color=this.plain?this.color:"white"),t.fontSize=this.textSize+"px",t},textSize:function(){var t=14,e=this.size;return"large"===e&&(t=16),"normal"===e&&(t=14),"small"===e&&(t=12),"mini"===e&&(t=10),t}},methods:{clickHandler:function(){var t=this;this.disabled||this.loading||uni.$u.throttle((function(){t.$emit("click")}),this.throttleTime)},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=a},"6d71":function(t,e,o){"use strict";o.r(e);var n=o("6679"),i=o.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);e["default"]=i.a},"7a03":function(t,e,o){var n=o("98f4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=o("4f06").default;i("41c8995e",n,!0,{sourceMap:!1,shadowMode:!1})},"85ca":function(t,e,o){var n=o("24fb");e=n(!1),e.push([t.i,'uni-view[data-v-26861ad0], uni-scroll-view[data-v-26861ad0], uni-swiper-item[data-v-26861ad0]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-loading-icon[data-v-26861ad0]{flex-direction:row;align-items:center;justify-content:center;color:#c8c9cc}.u-loading-icon__text[data-v-26861ad0]{margin-left:4px;color:#606266;font-size:14px;line-height:20px}.u-loading-icon__spinner[data-v-26861ad0]{width:30px;height:30px;position:relative;box-sizing:border-box;max-width:100%;max-height:100%;-webkit-animation:u-rotate-data-v-26861ad0 1s linear infinite;animation:u-rotate-data-v-26861ad0 1s linear infinite}.u-loading-icon__spinner--semicircle[data-v-26861ad0]{border-width:2px;border-color:transparent;border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;border-style:solid}.u-loading-icon__spinner--circle[data-v-26861ad0]{border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;border-width:2px;border-top-color:#e5e5e5;border-right-color:#e5e5e5;border-bottom-color:#e5e5e5;border-left-color:#e5e5e5;border-style:solid}.u-loading-icon--vertical[data-v-26861ad0]{flex-direction:column}[data-v-26861ad0]:host{font-size:0;line-height:1}.u-loading-icon__spinner--spinner[data-v-26861ad0]{-webkit-animation-timing-function:steps(12);animation-timing-function:steps(12)}.u-loading-icon__text[data-v-26861ad0]:empty{display:none}.u-loading-icon--vertical .u-loading-icon__text[data-v-26861ad0]{margin:6px 0 0;color:#606266}.u-loading-icon__dot[data-v-26861ad0]{position:absolute;top:0;left:0;width:100%;height:100%}.u-loading-icon__dot[data-v-26861ad0]:before{display:block;width:2px;height:25%;margin:0 auto;background-color:currentColor;border-radius:40%;content:" "}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(1){-webkit-transform:rotate(30deg);transform:rotate(30deg);opacity:1}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(2){-webkit-transform:rotate(60deg);transform:rotate(60deg);opacity:.9375}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(3){-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:.875}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(4){-webkit-transform:rotate(120deg);transform:rotate(120deg);opacity:.8125}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(5){-webkit-transform:rotate(150deg);transform:rotate(150deg);opacity:.75}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(6){-webkit-transform:rotate(180deg);transform:rotate(180deg);opacity:.6875}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(7){-webkit-transform:rotate(210deg);transform:rotate(210deg);opacity:.625}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(8){-webkit-transform:rotate(240deg);transform:rotate(240deg);opacity:.5625}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(9){-webkit-transform:rotate(270deg);transform:rotate(270deg);opacity:.5}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(10){-webkit-transform:rotate(300deg);transform:rotate(300deg);opacity:.4375}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(11){-webkit-transform:rotate(330deg);transform:rotate(330deg);opacity:.375}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(12){-webkit-transform:rotate(1turn);transform:rotate(1turn);opacity:.3125}@-webkit-keyframes u-rotate-data-v-26861ad0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-rotate-data-v-26861ad0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},"8e8e":function(t,e,o){var n=o("48cc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=o("4f06").default;i("5c529306",n,!0,{sourceMap:!1,shadowMode:!1})},9381:function(t,e,o){"use strict";o.r(e);var n=o("e557"),i=o("6d71");for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);o("a9b5");var r=o("f0c5"),d=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"7323bf5d",null,!1,n["a"],void 0);e["default"]=d.exports},"98f4":function(t,e,o){var n=o("24fb");e=n(!1),e.push([t.i,'uni-view[data-v-7323bf5d], uni-scroll-view[data-v-7323bf5d], uni-swiper-item[data-v-7323bf5d]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-button[data-v-7323bf5d]{width:100%}.u-button__text[data-v-7323bf5d]{white-space:nowrap;line-height:1}.u-button[data-v-7323bf5d]:before{position:absolute;top:50%;left:50%;width:100%;height:100%;border:inherit;border-radius:inherit;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);opacity:0;content:" ";background-color:#000;border-color:#000}.u-button--active[data-v-7323bf5d]:before{opacity:.15}.u-button__icon + .u-button__text[data-v-7323bf5d]:not(:empty), .u-button__loading-text[data-v-7323bf5d]{margin-left:4px}.u-button--plain.u-button--primary[data-v-7323bf5d]{color:#3c9cff}.u-button--plain.u-button--info[data-v-7323bf5d]{color:#909399}.u-button--plain.u-button--success[data-v-7323bf5d]{color:#5ac725}.u-button--plain.u-button--error[data-v-7323bf5d]{color:#f56c6c}.u-button--plain.u-button--warning[data-v-7323bf5d]{color:#f56c6c}.u-button[data-v-7323bf5d]{height:40px;position:relative;align-items:center;justify-content:center;display:flex;flex-direction:row;box-sizing:border-box;flex-direction:row}.u-button__text[data-v-7323bf5d]{font-size:15px}.u-button__loading-text[data-v-7323bf5d]{font-size:15px;margin-left:4px}.u-button--large[data-v-7323bf5d]{width:100%;height:50px;padding:0 15px}.u-button--normal[data-v-7323bf5d]{padding:0 12px;font-size:14px}.u-button--small[data-v-7323bf5d]{min-width:60px;height:30px;padding:0 8px;font-size:12px}.u-button--mini[data-v-7323bf5d]{height:22px;font-size:10px;min-width:50px;padding:0 8px}.u-button--disabled[data-v-7323bf5d]{opacity:.5}.u-button--info[data-v-7323bf5d]{color:#323233;background-color:#fff;border-color:#ebedf0;border-width:1px;border-style:solid}.u-button--success[data-v-7323bf5d]{color:#fff;background-color:#5ac725;border-color:#5ac725;border-width:1px;border-style:solid}.u-button--primary[data-v-7323bf5d]{color:#fff;background-color:#3c9cff;border-color:#3c9cff;border-width:1px;border-style:solid}.u-button--error[data-v-7323bf5d]{color:#fff;background-color:#f56c6c;border-color:#f56c6c;border-width:1px;border-style:solid}.u-button--warning[data-v-7323bf5d]{color:#fff;background-color:#f9ae3d;border-color:#f9ae3d;border-width:1px;border-style:solid}.u-button--block[data-v-7323bf5d]{display:flex;flex-direction:row;width:100%}.u-button--circle[data-v-7323bf5d]{border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px}.u-button--square[data-v-7323bf5d]{border-bottom-left-radius:3px;border-bottom-right-radius:3px;border-top-left-radius:3px;border-top-right-radius:3px}.u-button__icon[data-v-7323bf5d]{min-width:1em;line-height:inherit!important;vertical-align:top}.u-button--plain[data-v-7323bf5d]{background-color:#fff}.u-button--hairline[data-v-7323bf5d]{border-width:.5px!important}',""]),t.exports=e},a9b5:function(t,e,o){"use strict";var n=o("7a03"),i=o.n(n);i.a},adbe:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return t.show?o("v-uni-view",{staticClass:"u-loading-icon",class:[t.vertical&&"u-loading-icon--vertical"],style:[t.$u.addStyle(t.customStyle)]},[t.webviewHide?t._e():o("v-uni-view",{ref:"ani",staticClass:"u-loading-icon__spinner",class:["u-loading-icon__spinner--"+t.mode],style:{color:t.color,width:t.$u.addUnit(t.size),height:t.$u.addUnit(t.size),borderTopColor:t.color,borderBottomColor:t.otherBorderColor,borderLeftColor:t.otherBorderColor,borderRightColor:t.otherBorderColor,"animation-duration":t.duration+"ms","animation-timing-function":"semicircle"===t.mode||"circle"===t.mode?t.timingFunction:""}},["spinner"===t.mode?t._l(t.array12,(function(t,e){return o("v-uni-view",{key:e,staticClass:"u-loading-icon__dot"})})):t._e()],2),t.text?o("v-uni-text",{staticClass:"u-loading-icon__text",style:{fontSize:t.$u.addUnit(t.textSize),color:t.textColor}},[t._v(t._s(t.text))]):t._e()],1):t._e()},i=[]},c7fa:function(t,e,o){"use strict";o.r(e);var n=o("6072"),i=o.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);e["default"]=i.a},e305e:function(t,e,o){"use strict";o("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("a9e3");var n={props:{hairline:{type:Boolean,default:uni.$u.props.button.hairline},type:{type:String,default:uni.$u.props.button.type},size:{type:String,default:uni.$u.props.button.size},shape:{type:String,default:uni.$u.props.button.shape},plain:{type:Boolean,default:uni.$u.props.button.plain},disabled:{type:Boolean,default:uni.$u.props.button.disabled},loading:{type:Boolean,default:uni.$u.props.button.loading},loadingText:{type:[String,Number],default:uni.$u.props.button.loadingText},loadingMode:{type:String,default:uni.$u.props.button.loadingMode},loadingSize:{type:[String,Number],default:uni.$u.props.button.loadingSize},openType:{type:String,default:uni.$u.props.button.openType},formType:{type:String,default:uni.$u.props.button.formType},appParameter:{type:String,default:uni.$u.props.button.appParameter},hoverStopPropagation:{type:Boolean,default:uni.$u.props.button.hoverStopPropagation},lang:{type:String,default:uni.$u.props.button.lang},sessionFrom:{type:String,default:uni.$u.props.button.sessionFrom},sendMessageTitle:{type:String,default:uni.$u.props.button.sendMessageTitle},sendMessagePath:{type:String,default:uni.$u.props.button.sendMessagePath},sendMessageImg:{type:String,default:uni.$u.props.button.sendMessageImg},showMessageCard:{type:Boolean,default:uni.$u.props.button.showMessageCard},dataName:{type:String,default:uni.$u.props.button.dataName},throttleTime:{type:[String,Number],default:uni.$u.props.button.throttleTime},hoverStartTime:{type:[String,Number],default:uni.$u.props.button.hoverStartTime},hoverStayTime:{type:[String,Number],default:uni.$u.props.button.hoverStayTime},text:{type:[String,Number],default:uni.$u.props.button.text},icon:{type:String,default:uni.$u.props.button.icon},iconColor:{type:String,default:uni.$u.props.button.icon},color:{type:String,default:uni.$u.props.button.color}}};e.default=n},e514:function(t,e,o){var n=o("85ca");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=o("4f06").default;i("61b6d1e8",n,!0,{sourceMap:!1,shadowMode:!1})},e557:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return n}));var n={uLoadingIcon:o("3715").default,uIcon:o("98a6").default},i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-button",{staticClass:"u-button u-reset-button",class:t.bemClass,style:[t.baseColor,t.$u.addStyle(t.customStyle)],attrs:{"hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":t.sendMessagePath,lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.disabled||t.loading?"":"u-button--active"},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[t.loading?[o("u-loading-icon",{attrs:{mode:t.loadingMode,size:1.15*t.loadingSize,color:t.loadingColor}}),o("v-uni-text",{staticClass:"u-button__loading-text",style:[{fontSize:t.textSize+"px"}]},[t._v(t._s(t.loadingText||t.text))])]:[t.icon?o("u-icon",{attrs:{name:t.icon,color:t.iconColorCom,size:1.35*t.textSize,customStyle:{marginRight:"2px"}}}):t._e(),t._t("default",[o("v-uni-text",{staticClass:"u-button__text",style:[{fontSize:t.textSize+"px"}]},[t._v(t._s(t.text))])])]],2)},a=[]},ec5f:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return n}));var n={uNavbar:o("0f78").default,uCellGroup:o("5cf7").default,uCell:o("2453").default,uButton:o("9381").default,uPopup:o("b82b").default},i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("u-navbar",{attrs:{title:"转岗确认",autoBack:!0},on:{rightClick:function(e){arguments[0]=e=t.$handleEvent(e),t.rightClick.apply(void 0,arguments)}}}),o("v-uni-view",{staticClass:"main"},[o("v-uni-view",{staticClass:"content"},[o("v-uni-view",{staticClass:"info"},[o("v-uni-view",[t._v("姓名："+t._s(t.reorientationInfo.name))]),o("v-uni-view",[t._v("内容："+t._s(t.reorientationInfo.content))]),o("v-uni-view",[t._v("转岗时间："+t._s(t.reorientationInfo.times))]),o("v-uni-view",[t._v("备注："+t._s(t.reorientationInfo.remarks))])],1),o("u-cell-group",{staticStyle:{"margin-top":"20px"}},[o("u-cell",{attrs:{title:"职业病危害告知书",isLink:!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.lookDetail.apply(void 0,arguments)}}})],1)],1)],1),o("v-uni-view",{staticClass:"bottom"},[o("u-button",{staticClass:"btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmSign.apply(void 0,arguments)}}},[t._v("确认签字")]),o("v-uni-view",{staticClass:"info"},[t._v("提示：该页面签字视为您已查看确认过所有文件")])],1),o("u-popup",{attrs:{mode:"bottom",show:t.show},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}},[o("Notification",{attrs:{officialSeal:t.config.imgUrl+t.reorientationInfo.officialSeal,hazardNotifications:t.reorientationInfo.harmFactors},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}})],1),o("u-popup",{attrs:{catchtouchmove:"true",overlayStyle:{"touch-action":"none"},mode:"bottom",show:t.showSignPanel},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSignPanel.apply(void 0,arguments)}}},[o("SignPanel",{attrs:{EnterpriseID:t.reorientationInfo.EnterpriseID,reorientationInfo:t.reorientationInfo,reorientationId:t.reorientationInfo.id,detailId:t.reorientationInfo.detailId},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSignPanel.apply(void 0,arguments)}}})],1)],1)},a=[]},eee7:function(t,e,o){"use strict";o.r(e);var n=o("02c6"),i=o.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);e["default"]=i.a}}]);