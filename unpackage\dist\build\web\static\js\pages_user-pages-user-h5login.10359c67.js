(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-h5login"],{2149:function(e,t,n){"use strict";(function(e){n("7a82");var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("25f0"),n("466d"),n("841c");var o=a(n("c397")),s={data:function(){return{h5code:"",showCompanysFlag:!1}},onLoad:function(t){var n=this.getUrlParameter("code");e("log","vurl",n," at pages_user/pages/user/h5login.vue:21"),t&&t.code?this.h5code=t.code:this.h5code=n,this.getAccessToken(),e("log","data",t," at pages_user/pages/user/h5login.vue:28")},methods:{getAccessToken:function(){var t=this;e("log","获取token",this.h5code," at pages_user/pages/user/h5login.vue:32"),o.default.wxWorkAuthAction({code:this.h5code}).then((function(n){e("log","获取token",n," at pages_user/pages/user/h5login.vue:38"),200===n.status?(n.data.userToken&&uni.setStorageSync("userToken",n.data.userToken),uni.showToast({title:n.message,icon:"success"}),t.goHome()):(uni.showToast({title:n.message,icon:"none"}),setTimeout((function(){uni.navigateTo({url:"/pages/login/login"})}),1e3))}))},getUrlParameter:function(t){e("log","路径"," at pages_user/pages/user/h5login.vue:60"),e("log",window.location," at pages_user/pages/user/h5login.vue:62");var n=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),a=window.location.search.substr(1).match(n);return null!=a?unescape(a[2]):null},goHome:function(){this.showCompanysFlag=!1,this.$store.dispatch("/user/loginVerification"),setTimeout((function(){uni.reLaunch({url:"/pages/index/index"})}),1e3)}}};t.default=s}).call(this,n("0de9")["log"])},"61ee":function(e,t,n){"use strict";n.r(t);var a=n("2149"),o=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(s);t["default"]=o.a},"82f7":function(e,t,n){var a=n("ce16");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=n("4f06").default;o("7b915eda",a,!0,{sourceMap:!1,shadowMode:!1})},ce16:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,".public-web-container[data-v-4206f070]{display:flex;flex-direction:column;align-items:center}@-webkit-keyframes breathe-data-v-4206f070{0%{opacity:0}50%{opacity:1}100%{opacity:0}}@keyframes breathe-data-v-4206f070{0%{opacity:0}50%{opacity:1}100%{opacity:0}}",""]),e.exports=t},e52d:function(e,t,n){"use strict";var a=n("82f7"),o=n.n(a);o.a},f33d:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c;return this._m(0)},o=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"public-web-container breathe"},[t("p",[this._v("正在跳转登录中...")])])}]},fea3:function(e,t,n){"use strict";n.r(t);var a=n("f33d"),o=n("61ee");for(var s in o)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(s);n("e52d");var i=n("f0c5"),u=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,"4206f070",null,!1,a["a"],void 0);t["default"]=u.exports}}]);