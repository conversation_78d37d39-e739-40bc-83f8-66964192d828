<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="防护用品领用" />
		<view slot="gBody" class="tabs-container">
			<!-- 添加遮罩层，仅在标签切换过程中显示 -->
			<view v-if="isTabSwitching" class="tab-mask"></view>
			
			<u-tabs :list="tabList" :scrollable="true" @click="changeView" lineWidth="30" :activeStyle="{
					color: '#333333',
					fontWeight: 'bold',
				}" :inactiveStyle="{
					color: '#000000',
				}" itemStyle="padding: 0.5rem 0.5rem 0 1rem; height: 2rem;"></u-tabs>
			
			<!-- 领用 -->
			<view v-show="currentIndex === 0" class="tab-content">
				<view v-if="waitRecords.length > 0">{{  }}
					<view class="receiveBox" v-for="item in waitApplyRecords" :key="item._id">
						<view class="contain">
							<view class="mainBox">
								<view class="recordTitle">{{ item.product }} {{ item.number }}件</view>
								<view class="type online" v-if="item.recordSource === 1">线上申请</view>
								<view class="type under" v-else>线下领用</view>
								<view class="content">
									<view v-for="product in item.products" :key="product._id">{{ product.product }}
										{{ product.modelNumber ? product.modelNumber : '' }} × {{ product.number }}
									</view>
								</view>
							</view>
							<view class="btnBar">
								<u-button type="primary" text="领用" @tap="receiveBtn(item, 'receive')"
									size="small"></u-button>
								<u-button type="warning" text="拒绝" @tap="receiveBtn(item, 'reject')"
									size="small"></u-button>
							</view>
						</view>
						<view class="timeBox">
							<view class="timeNotice" v-if="item.receiveStartDate" :class="warning ? warn : ''">
								请{{ item.warningDate ? '在' + item.warningDate + '前' : '及时' }}完成领取</view>
						</view>
					</view>
				</view>

				<view>
					<view class="receiveBox" v-for="item in allProtectionPlan" :key="item._id">
						<view class="receiveBoxTitle" v-if="item.grantType === 'mill'">
							工作场所：{{item.workspacesName ? item.workspacesName : ''}}/{{item.workstationName ? item.workstationName : ''}}
						</view>
						<view class="receiveBoxTitle" v-if="item.grantType === 'depart'">
							部门：{{item.departName}}
						</view>

						<view>
							<view style="border-bottom: 1px solid #c8c9cc;padding-bottom: 10px;margin-bottom: 10px;"
								class="contain" v-for="productItem in item.products" :key="productItem._id">
								<view class="mainBox">
									<view class="recordTitle">{{productItem.product}} * {{productItem.number}} </view>
									<view style="display: flex;">
										<view class="type under" style="padding-right: 0.5rem;">线下领用 </view>
										<view class="type under">
											{{productItem.time}}{{formatTimeUnit(productItem.timeUnit)}}/次
										</view>
									</view>
									<view class="content">
										领取时间：{{ formatTime(productItem.todo.receiveStartDate) }}
									</view>
								</view>
								<view class="btnBar" v-if="ableReceive(productItem.todo.receiveStartDate)">
									<u-button type="primary" text="领用"
										@tap="handleReceive(productItem, item, 'receive')" size="small"></u-button>
									<u-button type="warning" text="拒绝" @tap="handleReceive(productItem, item, 'reject')"
										size="small"></u-button>
								</view>
								<view style="width: 200px;" class="btnBar" v-else>
									<u-button type="info" text="未到领取时间" size="small"></u-button>
								</view>
							</view>
						</view>
					</view>

					<view v-if="allProtectionPlan.length === 0 && waitApplyRecords.length===0" class="empty-state-simple">
						<view class="empty-icon">📦</view>
						<view class="empty-text">暂无可领用的用品</view>
					</view>
				</view>
			</view>
			
			<!-- 申请 -->
			<view v-show="currentIndex === 1" class="tab-content center">
				<view v-if="appShow" class="list">
					<view v-if="userInfo.branch === 'wh'" class="center-body">
						<view class="category-container">
							<!-- 空状态 -->
							<view v-if="navLeft.length === 0" class="empty-state">
								<view class="empty-icon">📦</view>
								<view class="empty-text">暂无可申请的防护用品</view>
								<view class="empty-subtitle">请联系管理员添加防护用品</view>
							</view>

							<view v-for="item in navLeft" :key="item._id" class="category-card">
								<!-- 分类头部 -->
								<view class="category-header" @click="toggleExpand(item)">
									<view class="category-info">
										<view class="category-icon" :data-category="getCategoryType(item.name)">
											<text class="category-letter">{{ getCategoryLetter(item.name) }}</text>
										</view>
										<view class="category-details">
											<view class="category-title">{{ item.name }}</view>
											<view class="category-subtitle">{{ formatCategoryPath(item.categoryPath, item.name) }}</view>
											<view class="category-count">{{ item.data.length }} 种产品</view>
										</view>
									</view>
									<view class="expand-button">
										<text class="expand-icon">{{ item.isExpanded ? '▲' : '▼' }}</text>
									</view>
								</view>

								<!-- 产品列表 -->
								<view v-if="item.isExpanded" class="products-grid">
									<view v-for="subItem in item.data" :key="subItem._id"
										  class="product-card"
										  :class="{ 'selected': subItem.selectedNum > 0, 'low-stock': (subItem.surplus || 0) < 10 }">
										<view class="product-info">
											<view class="product-title">{{ subItem.product || subItem.productSpec }}</view>
											<view class="product-spec">{{ subItem.productSpec || subItem.modelNumber || '标准规格' }}</view>
											<view class="product-stock" :class="{ 'low-stock': (subItem.surplus || 0) < 10 }">
												库存: {{ subItem.surplus || 0 }}
											</view>
										</view>
										<view class="product-actions">
											<view class="quantity-selector" :class="{ 'active': subItem.selectedNum > 0 }">
												<view class="quantity-btn minus"
													  :class="{ disabled: subItem.selectedNum <= 0 }"
													  @click="decreaseQuantity(subItem, item)">
													<u-icon name="minus" size="12" color="#fff"></u-icon>
												</view>
												<view class="quantity-display">{{ subItem.selectedNum || 0 }}</view>
												<view class="quantity-btn plus"
													  :class="{ disabled: subItem.selectedNum >= (subItem.surplus || 100) }"
													  @click="increaseQuantity(subItem, item)">
													<u-icon name="plus" size="12" color="#fff"></u-icon>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="center-body">
						<view class="category-container">
							<!-- 空状态 -->
							<view v-if="navLeft.length === 0" class="empty-state">
								<view class="empty-icon">📦</view>
								<view class="empty-text">暂无可申请的防护用品</view>
								<view class="empty-subtitle">请联系管理员添加防护用品</view>
							</view>

							<view v-for="item in navLeft" :key="item._id" class="category-card">
								<!-- 分类头部 -->
								<view class="category-header" @click="toggleExpand(item)">
									<view class="category-info">
										<view class="category-icon" :data-category="getCategoryType(item.name)">
											<text class="category-letter">{{ getCategoryLetter(item.name) }}</text>
										</view>
										<view class="category-details">
											<view class="category-title">{{ item.name }}</view>
											<view class="category-subtitle">{{ formatCategoryPath(item.categoryPath, item.name) }}</view>
											<view class="category-count">{{ item.data.length }} 种产品</view>
										</view>
									</view>
									<view class="expand-button">
										<text class="expand-icon">{{ item.isExpanded ? '▲' : '▼' }}</text>
									</view>
								</view>

								<!-- 产品列表 -->
								<view v-if="item.isExpanded" class="products-grid">
									<view v-for="subItem in item.data" :key="subItem._id"
										  class="product-card"
										  :class="{ 'selected': subItem.selectedNum > 0, 'low-stock': (subItem.surplus || 0) < 10 }">
										<view class="product-info">
											<view class="product-title">{{ subItem.product }}</view>
											<view class="product-spec">{{ subItem.productSpec || subItem.modelNumber || '标准规格' }}</view>
											<view class="product-stock" :class="{ 'low-stock': (subItem.surplus || 0) < 10 }">
												库存: {{ subItem.surplus || 0 }}
											</view>
										</view>
										<view class="product-actions">
											<view class="quantity-selector" :class="{ 'active': subItem.selectedNum > 0 }">
												<view class="quantity-btn minus"
													  :class="{ disabled: subItem.selectedNum <= 0 }"
													  @click="decreaseQuantity(subItem, item)">
													<u-icon name="minus" size="12" color="#fff"></u-icon>
												</view>
												<view class="quantity-display">{{ subItem.selectedNum || 0 }}</view>
												<view class="quantity-btn plus"
													  :class="{ disabled: subItem.selectedNum >= (subItem.surplus || 100) }"
													  @click="increaseQuantity(subItem, item)">
													<u-icon name="plus" size="12" color="#fff"></u-icon>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else>
					<view>
						<u-button type="primary" text="添加新的用品申请" @click="appShow = true"></u-button>
					</view>
					<view>


						<view class="applicationBox" v-for="item in askRecords" :key="item._id">
							<view class="mainBox">
								<view class="recordTitle">{{ item.products[0].product }} × {{ item.products[0].number }}
								</view>
								<view class="note">
									申请类型：{{ claimTypeList.find(type => type.value === item.claimType)&&claimTypeList.find(type => type.value == item.claimType).name || '-' }}
								</view>
								<view class="note">申请理由：{{ item.notes }}</view>
								<view class="note">申请时间：{{ formatTime(item.createdAt ) }}</view>
								<u-steps :current="item.auditStatus+1" direction="column">
									<u-steps-item title="已申请" :desc="userInfo.name">
									</u-steps-item>
									<u-steps-item v-if="item.auditStatus===0" title="待审核" desc="管理员">
									</u-steps-item>
									<u-steps-item v-if="item.auditStatus===1" title="已审核" desc="管理员">
									</u-steps-item>
									<u-steps-item v-if="item.auditStatus===2" title="已拒绝" desc="管理员"
										:view="item.reason">
									</u-steps-item>
								</u-steps>
							</view>
						</view>

					</view>
				</view>
			</view>
			
			<!-- 记录 -->
			<view v-show="currentIndex === 2" class="tab-content">
				<view class="recordBox" v-for="item in receiveRecords" :key="item._id">
					<view class="recordTitle">{{ item.product }}等{{ item.number }}件</view>
					<view class="type online" v-if="item.recordSource === 1">线上申请</view>
					<view class="type under" v-else>计划领用</view>
					<view class="content">
						<view v-for="product in item.products" :key="product._id">{{ product.product }}
							({{ product.productSpec ? product.productSpec : '-' }}
							{{ product.modelNumber ? product.modelNumber : '' }}) × {{ product.number }}
						</view>
					</view>
					<view class="bottomBar">
						<view class="status" v-if="item.isRejected === false">
							<span class="circle bGreen"></span>
							<span>
								已签字
							</span>
						</view>
						<view class="status" v-else>
							<span class="circle bRed"></span>
							<span>
								已拒绝
							</span>
						</view>
						<view class="time">{{ item.receiveDate }}</view>
					</view>
				</view>
			</view>
			
			<!-- 防护用品清单 -->
			<view v-show="currentIndex === 3" class="tab-content">
				<view class="recordBox" v-for="item in protectList" :key="item._id">
					<view class="recordTitle">{{ item.product }}等{{ item.number }}件</view>
					<view class="type online" v-if="item.recordSource === 1">线上申请</view>
					<view class="type under" v-else>计划领用</view>
					<view class="content">
						<view v-for="product in item.products" :key="product._id">{{ product.product }}
							({{ product.productSpec ? product.productSpec : '-' }}
							{{ product.modelNumber ? product.modelNumber : '' }}) × {{ product.number }}
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部确认申请 -->
			<view v-if="appShow" class="bottom-confirm">
				<view class="bottom-text" :class="{ 'has-selection': confirmNum > 0 }">
					<text v-if="confirmNum > 0">已选择 {{ confirmNum }} 种防护用品</text>
					<text v-else>请选择要申请的防护用品</text>
					<view v-if="confirmNum > 0" class="selected-summary">
						<text class="summary-text">点击确认申请提交申请</text>
					</view>
				</view>
				<view class="bottom-btn">
					<u-button type="primary"
							  :text="confirmNum > 0 ? `确认申请(${confirmNum})` : '请先选择产品'"
							  :disabled="confirmNum === 0"
							  :customStyle="getButtonStyle()"
							  @tap="confirmNum > 0 ? (dialogShow = true) : showSelectTip()"></u-button>
				</view>
			</view>
			<!-- 领用计划选择申请类型 -->
			<view>
				<u-action-sheet :actions="menuList" :safeAreaInsetBottom="true" round="10" cancelText="取消"
					:title="menuTitle" :show="menuShow" @select="selectType" @close="closeSelectType"></u-action-sheet>
			</view>
			<view>
				<u-modal style="z-index: 10075 !important;" :show="dialogShow" :title="dialogTitle"
					@cancel="dialogCancel" @confirm="dialogConfirm" :showCancelButton="true" confirmText="确认申请">
					<view class="slot-content">
						<!-- wh分支使用表单 -->
						<template v-if="userInfo.branch === 'wh'">
							<u--form labelPosition="left" :model="formData" ref="uForm">
								<u-form-item label="申请类型" prop="claimType" borderBottom @click="showApplyType = true;">
									<u--input v-model="formData.claimTypeLabel" placeholder="请选择申请类型"
										border="none"></u--input>
									<u-icon slot="right" name="arrow-right"></u-icon>
								</u-form-item>
								<u-form-item label="申请理由" prop="reason" borderBottom>
									<u--input v-model="formData.reason" placeholder="请输入申请理由" border="none"></u--input>
								</u-form-item>
							</u--form>
							<u-action-sheet :show="showApplyType" :actions="claimTypeList" title="请选择申请类型"
								@close="showApplyType = false" @select="applyTypeSelect">
							</u-action-sheet>
							<u-toast ref="uToast"></u-toast>
						</template>
						<!-- 其他分支使用简单输入框 -->
						<template v-else>
							<u--input placeholder="请输入内容" border="bottom" v-model="reason" clearable></u--input>
						</template>
					</view>
				</u-modal>
			</view>
			<u-modal :show="confirmShow" :title="confirmTitle" :content='confirmContent' @confirm="signClick"
				showCancelButton @cancel="() => confirmShow = false"></u-modal>

			<u-popup :show="showSelectProduct" mode="bottom" height="600px">
				<view :style="{ height: scrollHeight }">
					<view class="selectProductTitle">请选择领取防护用品的具体规格型号</view>
					<view class="selectProductBody">

						<view v-for="(item,index) in productsList" :key="index">
							<!-- 产品型号信息部分 -->
							<view class="product-section">
								<view class="product-title">{{item.name}} × {{item.receiveNum}}</view>

								<u-radio-group v-model="item.selectData" shape="square" placement="column" @change="e => radioGroupChange(e, item)">
									<u-radio
										class="radioClass"
										style="margin: 10px 0"
										v-for="product in item.children" 
										:key="product._id" 
										:name="product._id"
										:label="(hasDisabled(product, item) ? '库存不足 | ' : '' ) + '规格：' + product.productSpec + ' | ' + (product.modelNumber ? '型号：' + product.modelNumber + ' | ' : '')"
										:disabled="hasDisabled(product, item)">
									</u-radio>
								</u-radio-group>
							</view>

							<!-- 生产日期选择部分 -->
							<view class="date-section" v-if="item.selectData && needProductionDate(item)">
								<view class="date-section-title-container">
									<view class="date-section-title">{{ getProductName(item) }}生产日期</view>
									<view class="help-icon" @click="toggleHelp(item)" v-if="getProductExpiryInfo(item)">
										<u-icon name="question-circle" color="#2979ff" size="18"></u-icon>
									</view>
								</view>
								<view class="date-tip" v-if="item.showHelp && getProductExpiryInfo(item)">
									{{ getProductExpiryInfo(item) }}
								</view>
								<view class="date-selector" @click="openDatePicker(item)">
									<text class="date-text">{{ formatProductionDate(item.productionDate) || '请选择生产日期' }}</text>
									<u-icon name="arrow-right" size="14" color="#666"></u-icon>
								</view>
								<!-- 到期日期显示 -->
								<view class="expiry-date-display" v-if="item.productionDate && getProductExpiryInfo(item)">
									<text class="expiry-label">到期日期：</text>
									<text class="expiry-date">{{ calculateExpiryDate(item) }}</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 添加日期选择器组件 -->
					<u-datetime-picker :show="showPicker" v-model="productionDate" mode="date" @confirm="confirmDate"
						@cancel="showPicker = false"></u-datetime-picker>

					<view class="bottom-confirm">
						<u-button @click="closeSelectProduct">取消</u-button>
						<u-button type="primary" @click="confirmSelectProduct">确认</u-button>
					</view>
				</view>
			</u-popup>

		</view>
	</gracePage>
</template>
<script>
	import gracePage from "@/graceUI/components/gracePage.vue";
	import {
		mapGetters
	} from 'vuex'
	import config from "@/common.js";
	import employeeApi from '@/api/employee.js' //导入接口
	import moment from "moment";

export default {
	data() {
		return {
			timeUnit: [{
				label: "天",
				value: "d"
			},
			{
				label: "周",
				value: "w"
			},
			{
				label: "月",
				value: "M"
			},
			{
				label: "季",
				value: "Q"
			},
			{
				label: "年",
				value: "y"
			},
			],
			tabList: [{
				name: '领用计划'
			},
			{
				name: '领用申请'
			},
			{
				name: '领用记录'
			},
			{
				name: '在用防护'
			}
			],
			currentIndex: 0, // 当前页面索引值
			typeList: [],
			navLeft: [], // 申请列表左侧
			appShow: false, // 申请界面
			type: "error",
			count: 0,
			confirmNum: 0,
			confirmIds: [], // 确认申请
			menuTitle: '请选择领用类型',
			menuList: [{
				name: '到期更换',
				value: 1
			},
			{
				name: '以旧换新',
				value: 2
			},
			{
				name: '按标准发放',
				value: 3
			}
			],
			// 新增仓库信息字段
			employeeWarehouse: null,
			claimTypeList: [{
				name: '到期更换',
				value: '1'
			},
			{
				name: '以旧换新',
				value: '2'
			},
			{
				name: '其他',
				value: '3'
			}
			],
			showApplyType: false,
			menuShow: false,
			dialogShow: false,
			dialogTitle: '申请',
			reason: '', // 申请理由
			receiveRecords: [], // 领用记录
			protectList: [], // 防护用品清单
			allProtectionPlan: [], // 发放计划
			askRecords: [], // 申请记录
			waitRecords: [], // 待领用
			waitApplyRecords: [], // 自己申请待领用
			info: false,
			logo: 'https://avatar.bbs.miui.com/images/noavatar_small.gif',
			confirmShow: false, // 确认框
			confirmTitle: '请签字确认',
			confirmContent: '',
			claimType: '',
			query: {},
			showSelectProduct: false,
			productsId: '',
			productsSelector: [{
				cateName: '1',
				id: 1
			},
			{
				cateName: '2',
				id: 2
			}
			],
			scrollHeight: '600px',
			productsList: [],
			formData: {
				claimType: '',
				reason: '',
				claimTypeLabel: ''
			},
			showPicker: false,
			productionDate: Number(new Date()),
			currentProductItem: null, // 当前选择生产日期的产品项
			currentReceiveRecord: null, // 当前处理的领用记录
			showHelp: false,
			warning: false,
			isTabSwitching: false, // 添加标签切换状态标志
		}
	},
	async created() {
		if (this.userInfo.companyStatus != 2) {
			uni.showToast({
				title: '企业还未成功绑定',
				icon: "none"
			});
			return;
		}
		this.getDefendproducts();
		this.getUserInfo(this.userInfo)
	},

	computed: {
		...mapGetters(['hasLogin', 'userInfo', 'path', 'aboutTransfer']),
	},

	onShow() {
		// 恢复原有功能
		console.log('userInfo', this.userInfo);
		console.log('hasLogin', this.hasLogin);
		console.log('path', this.path);
		console.log('aboutTransfer', this.aboutTransfer);

		// 刷新数据（从签字页面返回时）
		if (this.hasLogin && this.userInfo) {
			this.refreshDynamicData();
		}

		uni.$on('refresh', data => {
			if (data.refresh) {
				this.refreshPage();
			}
		});
	},

	onReady() {
		// 页面渲染完成
	},

	onHide() {
		// 页面隐藏
	},

	onUnload() {
		// 页面卸载
	},

	onLoad() {
		// 页面加载
	},
	components: {
		gracePage,
	},
		methods: {
		ableReceive(e) {
			if (moment().isAfter(moment(e))) {
				return true
			}
			return false
		},
		formatTime(e) {
			if (e) {
				return moment(e).format('YYYY-MM-DD')
			} else {
				return '-'
			}
		},
		formatTimeUnit(e) {
			const target = this.timeUnit.find(item => item.value === e)
			if (target) {
				return target.label
			}
			return ''

		},
		refreshPage() {
			console.log('B -> A');
			uni.redirectTo({
				url: './ppe' //这是你的当前页面地址
			});
		},

		// 优化的changeView方法
		changeView(e) {
			if (this.currentIndex === e.index) return;
			// 设置切换状态，激活遮罩层
			this.isTabSwitching = true;

			// 延迟更新视图状态，避免渲染竞争
			setTimeout(() => {
				// 更新索引和状态
				this.currentIndex = e.index;

				// 视图更新后处理
				this.$nextTick(() => {
					// 短暂延迟后移除遮罩层
					setTimeout(() => {
						this.isTabSwitching = false;
					}, 100);
				});
			}, 50);
		},

		// 选择类型（使用新分类系统）
		selectType(e) {
			console.log('选择申请类型:', e);
			this.menuShow = false;
			this.query.claimType = e.value;

			// 如果是已申请的记录，直接确认领取
			if (this.query && this.query.data && this.query.data.recordSource == 1) {
				this.confirmShow = true;
				return
			}

			// 获取产品信息
			const products = this.query.data.products
			console.log('待处理产品:', products)

			const productsList = []
			products.forEach(product => {
				console.log('处理产品:', product)

				let categoryId = product.categoryId;
				let categoryPath = product.categoryPath;
				let categoryName = product.categoryName;

				// 如果没有categoryId，尝试从其他地方获取
				if (!categoryId) {
					// 检查是否是待领取记录，从记录本身获取分类信息
					if (this.query.data.categoryId) {
						categoryId = this.query.data.categoryId;
						categoryPath = this.query.data.categoryPath;
						categoryName = this.query.data.categoryName;
						console.log('从待领取记录获取分类信息:', { categoryId, categoryPath, categoryName });
					}
					// 如果还是没有，尝试通过产品名称匹配分类
					else if (product.product) {
						const matchedCategory = this.navLeft.find(category =>
							category.data && category.data.some(item =>
								item.product === product.product ||
								item.product.includes(product.product) ||
								product.product.includes(item.product)
							)
						);
						if (matchedCategory) {
							categoryId = matchedCategory._id;
							categoryPath = matchedCategory.categoryPath;
							categoryName = matchedCategory.name;
							console.log('通过产品名称匹配到分类:', { categoryId, categoryPath, categoryName });
						}
					}
				}

				// 使用categoryId匹配分类
				if (categoryId) {
					const targetCategory = this.navLeft.find(category => category._id === categoryId);

					if (targetCategory && targetCategory.data && targetCategory.data.length > 0) {
						productsList.push({
							name: categoryPath || categoryName || product.product,
							children: targetCategory.data, // 该分类下的所有具体产品
							categoryId: targetCategory._id,
							categoryName: targetCategory.name,
							selectData: '', // 用户选择的具体产品ID
							selectProductSpec: '',
							productsOrder: product._id,
							receiveNum: product.number || 1,
							// 标准信息
							standardInfo: {
								categoryId: categoryId,
								categoryPath: categoryPath,
								categoryName: categoryName,
								time: product.time,
								timeUnit: product.timeUnit
							}
						})
						console.log(`成功匹配分类: ${targetCategory.name}, 产品数量: ${targetCategory.data.length}`)
					} else {
						console.log(`未找到分类或分类下无产品: ${categoryId}`)
					}
				} else {
					console.log('无法获取分类信息，跳过:', product)
				}
			})

			this.productsList = productsList
			console.log('最终产品选择列表:', this.productsList)

			if (productsList.length > 0) {
				this.showSelectProduct = true
			} else {
				uni.showToast({
					title: '暂无可申请的产品',
					icon: 'none'
				})
			}
		},

		closeSelectType() {
			this.menuShow = false
		},

		// 格式化分类路径显示
		formatCategoryPath(categoryPath, categoryName) {
			if (categoryPath) {
				// 移除开头的斜杠，用 > 替换斜杠，让路径更清晰
				return categoryPath.replace(/^\//, '').replace(/\//g, ' > ');
			}
			return categoryName || '未分类';
		},

		// 获取分类类型（用于样式）
		getCategoryType(categoryName) {
			if (!categoryName) return 'default';
			const name = categoryName.toLowerCase();
			if (name.includes('头部') || name.includes('安全帽')) return 'head';
			if (name.includes('眼部') || name.includes('护目')) return 'eye';
			if (name.includes('呼吸') || name.includes('口罩') || name.includes('面罩')) return 'respiratory';
			if (name.includes('手部') || name.includes('手套')) return 'hand';
			if (name.includes('足部') || name.includes('鞋') || name.includes('靴')) return 'foot';
			if (name.includes('身体') || name.includes('防护服') || name.includes('工作服')) return 'body';
			if (name.includes('坠落') || name.includes('安全带') || name.includes('绳索')) return 'fall';
			return 'default';
		},

		// 获取分类首字母
		getCategoryLetter(categoryName) {
			if (!categoryName) return '?';
			// 提取中文首字符或前两个字符
			if (categoryName.length >= 2) {
				return categoryName.substring(0, 2);
			}
			return categoryName.charAt(0);
		},

		// 增加数量
		increaseQuantity(product, category) {
			const maxQuantity = product.surplus || 100;
			if ((product.selectedNum || 0) < maxQuantity) {
				this.$set(product, 'selectedNum', (product.selectedNum || 0) + 1);
				this.valChange(product, category);
			}
		},

		// 减少数量
		decreaseQuantity(product, category) {
			if ((product.selectedNum || 0) > 0) {
				this.$set(product, 'selectedNum', (product.selectedNum || 0) - 1);
				this.valChange(product, category);
			}
		},

		// 获取按钮样式
		getButtonStyle() {
			if (this.confirmNum > 0) {
				return {
					background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
					border: 'none',
					borderRadius: '25px',
					boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)'
				};
			}
			return {
				background: '#f5f5f5',
				color: '#999',
				border: 'none',
				borderRadius: '25px'
			};
		},

		// 显示选择提示
		showSelectTip() {
			// 轻微震动提示
			uni.vibrateShort();

			// 显示提示信息
			uni.showToast({
				title: '请先选择要申请的防护用品',
				icon: 'none',
				duration: 2000
			});

			// 如果有分类但没有展开，自动展开第一个分类
			if (this.navLeft.length > 0) {
				const firstCategory = this.navLeft[0];
				if (!firstCategory.isExpanded) {
					this.toggleExpand(firstCategory);
				}
			}
		},

		closeSelectProduct() {
			this.showSelectProduct = false
		},
		confirmSelectProduct() {
			const that = this;
			console.log('确认选择产品:', this.productsList);

			// 检查是否所有产品都已选择（如果只有一个产品则自动选择）
			for (let item of this.productsList) {
				if (!item.selectData && item.children && item.children.length > 1) {
					uni.showToast({
						title: '请选择所有产品的具体型号',
						duration: 2000,
						icon: 'error'
					});
					return
				}
				// 如果只有一个产品且未选择，自动选择
				if (!item.selectData && item.children && item.children.length === 1) {
					item.selectData = item.children[0]._id;
				}
			}

			// 检查需要生产日期的产品是否已填写生产日期
			for (let item of this.productsList) {
				if (this.needProductionDate(item) && !item.productionDate) {
					uni.showToast({
						title: `${this.getProductName(item)}需要填写生产日期`,
						duration: 2000,
						icon: 'error'
					});
					return
				}
			}

			uni.showModal({
				title: '确认申请',
				content: '确认申请这些防护用品？',
				success: function (res) {
					if (res.confirm) {
						// 构建申请产品数据（使用新分类系统）
						const selectedProducts = that.productsList.map(item => {
							const selectedProduct = item.children.find(child => child._id === item.selectData);

							return {
								// 新分类系统字段
								categoryId: item.categoryId,
								categoryName: item.categoryName,
								categoryPath: item.standardInfo.categoryPath,

								// 具体产品信息
								productId: item.selectData,
								product: selectedProduct.product,
								productSpec: selectedProduct.productSpec || '',
								modelNumber: selectedProduct.modelNumber || '',
								materialCode: selectedProduct.materialCode || '',

								// 申请数量
								number: item.receiveNum,

								// 配发标准信息
								planProductId: item.productsOrder,
								time: item.standardInfo.time,
								timeUnit: item.standardInfo.timeUnit
							}
						});

						// 🔑 关键修复：将选择的产品信息更新到领用记录中
						that.productsList.forEach(item => {
							if (item.selectData) {
								// 找到对应的领用记录产品
								const recordProduct = that.query.data.products.find(p =>
									p._id === item.productsOrder ||
									p.product === item.name.split('/').pop() ||
									p.categoryId === item.categoryId
								);

								if (recordProduct) {
									const selectedProduct = item.children.find(child => child._id === item.selectData);
									if (selectedProduct) {
										// 更新领用记录中的产品信息
										recordProduct.selectData = item.selectData;
										recordProduct.selectedProduct = selectedProduct;
										recordProduct.selectProductSpec = selectedProduct.productSpec;
										recordProduct.receiveNum = item.receiveNum;
										recordProduct.productionDate = item.productionDate;

										console.log('已更新领用记录产品信息:', {
											recordProductId: recordProduct._id,
											selectedProductId: item.selectData,
											selectedProduct: selectedProduct
										});
									}
								}
							}
						});

						// 保存选择的产品到query中，用于后续申请提交
						that.query.selectedProducts = selectedProducts;
						that.showSelectProduct = false;
						that.confirmShow = true;
					}
				}
			});
		},

		radioGroupChange(e, item) {
			let target = null
			this.navLeft.forEach(nL => {
				nL.data.forEach(product => {
					if (product._id === e) {
						target = product
					}
				})
			})

			if (target) {
				item.selectProductSpec = target.productSpec
				// item.modelNumber = target.modelNumber

				// 检查库存是否充足
				const requestNum = parseInt(item.receiveNum) || 1;
				const availableStock = parseInt(target.surplus) || 0;

				if (requestNum > availableStock) {
					uni.showToast({
						title: `库存不足，当前库存：${availableStock}，需要：${requestNum}`,
						duration: 3000,
						icon: 'error'
					});
					item.selectData = '';
					return;
				}

				// 🔑 关键修复：将选择的产品信息同步到领用记录中
				console.log('用户选择产品:', {
					productId: e,
					productName: target.product,
					spec: target.productSpec,
					requestNum,
					availableStock
				});

				// 找到对应的领用记录产品并更新selectData
				if (this.query && this.query.data && this.query.data.products) {
					const recordProduct = this.query.data.products.find(p =>
						p._id === item.productsOrder ||
						p.product === item.name.split('/').pop() ||
						p.categoryId === item.categoryId
					);

					if (recordProduct) {
						// 将用户选择的具体产品ID保存到领用记录中
						recordProduct.selectData = e;
						recordProduct.selectProductSpec = target.productSpec;
						recordProduct.selectedProduct = target;
						recordProduct.modelNumber = target.modelNumber;
						console.log('已更新领用记录产品:', recordProduct);
					} else {
						console.warn('未找到对应的领用记录产品:', {
							productsOrder: item.productsOrder,
							productName: item.name,
							categoryId: item.categoryId,
							availableProducts: this.query.data.products
						});
					}
				} else {
					console.error('领用记录数据不完整:', this.query);
				}
			}
		},

		hasDisabled(product, item) {
			if (!product.surplus) {
				return true
			}
			if (parseInt(item.receiveNum) > parseInt(product.surplus)) {
				return true
			}
			return false
		},

		// 对话框取消
		dialogCancel() {
			this.dialogShow = false;
			uni.showToast({
				title: "取消申请",
				icon: "none"
			});
		},

		// 确认申请提交
		async dialogConfirm() {
			let claimType = ''
			if (this.userInfo.branch === 'wh') {
				this.reason = this.formData.reason
				claimType = this.formData.claimType
			}

			// 验证申请类型（万华分支必填）
			if (this.userInfo.branch === 'wh' && !claimType) {
				this.$refs.uToast.show({
					type: "error",
					icon: false,
					message: "申请类型不能为空！",
				})
				return;
			}

			// 验证申请理由
			if (this.reason === '') {
				this.$refs.uToast.show({
					type: "error",
					icon: false,
					message: "申请理由不能为空！",
				})
				return;
			}

			this.dialogShow = false;
			this.appShow = false;

			// 构建申请参数（使用新分类系统数据结构）
			console.log('开始构建申请参数，confirmIds:', this.confirmIds);

			if (this.confirmIds.length === 0) {
				uni.showToast({
					title: '请先选择要申请的产品',
					icon: 'error'
				});
				return;
			}

			const applicationProducts = this.confirmIds.map(item => {
				console.log('处理申请产品:', item);
				return {
					// 分类信息
					categoryId: item.categoryId,
					categoryName: item.categoryName || item.name,
					categoryPath: item.categoryPath,

					// 产品信息
					productId: item._id,
					product: item.product || item.name,
					productSpec: item.productSpec || '',
					modelNumber: item.modelNumber || '',
					materialCode: item.materialCode || '',

					// 申请信息
					number: item.num || item.selectedNum || 1,
					notes: this.reason,
					claimType: claimType,

					// 员工信息
					employee: item.employee || this.userInfo.employeeId,
					employeeName: item.employeeName || this.userInfo.name,

					// 仓库信息
					warehouseId: this.employeeWarehouse ? this.employeeWarehouse.warehouseId : ''
				};
			});

			const params = {
				EnterpriseID: this.userInfo.companyId.length ? this.userInfo.companyId[0] : '',
				type: 'application',
				arr: applicationProducts
			}

			console.log('提交申请参数:', params);

			try {
				console.log('发送申请请求，参数:', params);
				const response = await employeeApi.receiveProducts(params);
				console.log('申请提交响应:', response);

				uni.showToast({
					title: '申请提交成功',
					icon: 'success'
				});

				// 清空选择状态
				this.confirmIds = [];
				this.confirmNum = 0;

				// 重置产品选择数量
				this.navLeft.forEach(category => {
					if (category.data && Array.isArray(category.data)) {
						category.data.forEach(product => {
							product.selectedNum = 0;
						});
					}
				});

				// 刷新页面数据
				setTimeout(() => {
					uni.redirectTo({
						url: './ppe'
					});
				}, 1500);
			} catch (error) {
				console.error('申请提交失败:', error);
				console.error('错误详情:', error.response || error.message);

				let errorMessage = '申请提交失败';
				if (error.response && error.response.data && error.response.data.message) {
					errorMessage = error.response.data.message;
				} else if (error.message) {
					errorMessage = error.message;
				}

				uni.showModal({
					title: '申请失败',
					content: errorMessage,
					showCancel: false
				});
			}
		},

		// 添加用品
		async valChange(val, item) {
			console.log('valChange 被调用:', val, item);

			// 确保有选择数量才处理
			if (!val.selectedNum || val.selectedNum <= 0) {
				// 如果数量为0，从确认列表中移除
				this.confirmIds = this.confirmIds.filter(obj => obj._id !== val._id);
				this.confirmNum = this.confirmIds.length;
				console.log('移除产品，当前确认列表:', this.confirmIds);
				return;
			}

			setTimeout(() => {
				let temp = false; // 判断是否已存在这个数据

				// 检查是否已经在确认列表中
				this.confirmIds.forEach(confirmItem => {
					if (confirmItem._id === val._id) {
						confirmItem.num = val.selectedNum;
						temp = true;
						console.log('更新现有产品数量:', confirmItem);
						return;
					}
				});

				// 如果不存在，添加新的
				if (temp === false) {
					const obj = {
						_id: val._id,
						num: val.selectedNum,
						employee: this.userInfo.employeeId,
						employeeName: this.userInfo.name,
						product: val.product,
						productSpec: val.productSpec || '',
						modelNumber: val.modelNumber || '',
						materialCode: val.materialCode || '',
						// 分类信息
						categoryId: item._id,
						categoryName: item.name,
						categoryPath: item.categoryPath,
					};

					// 万华分支的特殊处理
					if (this.userInfo.branch === 'wh') {
						obj.productSpecId = val._id;
						obj.name = val.product;
					}

					this.confirmIds.push(obj);
					console.log('添加新产品:', obj);
				}

				// 过滤掉数量为0的项目
				this.confirmIds = this.confirmIds.filter(obj => obj.num > 0);
				this.confirmNum = this.confirmIds.length;

				console.log('当前确认列表:', this.confirmIds);
				console.log('确认数量:', this.confirmNum);
			}, 50);
		},

		getUserInfo(val) {
			const loginType = uni.getStorageSync('loginType')
			if (!this.hasLogin) return
			if (loginType && val[loginType]) {
				debugger
				this.logo = val[loginType].logo || 'https://avatar.bbs.miui.com/images/noavatar_small.gif'
				this.userName = val[loginType].nickName
			} else {
				this.logo = val.logo || 'https://avatar.bbs.miui.com/images/noavatar_small.gif'
				this.userName = val.name || val.userName
			}
		},

		// 轻量级数据刷新（只刷新动态数据）
		async refreshDynamicData() {
			try {
				const userInfo = this.userInfo
				const res = await employeeApi.getDefendproducts(userInfo);
				if (res.status === 200) {
					// 只处理动态数据：领用记录和申请记录
					this.processRecords(res.data.records || []);
					this.processApplications(res.data.applications || []);

					// 更新配发标准（可能有新的待领取记录）
					this.allProtectionPlan = res.data.allProtectionPlan || []
				}
			} catch (error) {
				console.error('刷新数据失败:', error);
			}
		},

		// 处理领用记录
		processRecords(records) {
			// 清空之前的数据
			this.waitRecords = [];
			this.waitApplyRecords = [];
			this.receiveRecords = [];

			records.forEach(record => {
				if (record.products) {
					for (let i = 0; i < record.products.length; i++) {
						const item = record.products[i];
						record.product = record.products[0].product
						record.number = record.number ? record.number + item.number : item.number;
					}
				}
				if (!record.receiveDate) { // 没有被领取
					this.waitRecords.push(record);
					// 如果是申请类型的记录，也添加到申请待领取列表
					if (record.recordSource === 1) {
						this.waitApplyRecords.push(record);
					}
				} else { // 已经被领取
					this.receiveRecords.push(record)
				}
			});
			console.log(this.waitApplyRecords,'waitApplyRecords');
			console.log(this.waitRecords,'waitRecords');
			console.log(this.receiveRecords,'receiveRecords');
			
			// 防护用品清单
			this.protectList = this.receiveRecords.filter(item => !item.scrap && item.sign && !item.isRejected);
		},

		// 处理申请记录
		processApplications(applications) {
			this.askRecords = [];
			applications
				.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
				.forEach(application => {
					this.askRecords.push(application);
				});
		},

		async getDefendproducts() {
			// 获取防护用品领用记录（适配新的API响应结构）
			const userInfo = this.userInfo
			const res = await employeeApi.getDefendproducts(userInfo);
			if (res.status === 200) {
				// 配发标准
				this.allProtectionPlan = res.data.allProtectionPlan || []

				// 仓库信息（新增）
				if (res.data.employeeWarehouse) {
					this.employeeWarehouse = res.data.employeeWarehouse
					console.log('员工仓库信息:', this.employeeWarehouse)
					console.log('API返回的完整数据:', res.data)
				}

				// 处理领用记录
				this.processRecords(res.data.records || []);

				// 用品类型（适配新的数据结构）
				if (res.data.typeList && res.data.typeList.list) {
					console.log('防护用品分类数据（已按仓库过滤）:', res.data.typeList.list)
					res.data.typeList.list.forEach(item => {
						let products = '';
						if (item.data && Array.isArray(item.data)) {
							console.log(`分类 ${item.name} 的产品数量:`, item.data.length)
							item.data.forEach(item1 => {
								if (products === '') {
									products = item1.modelNumber || item1.productSpec || '';
								} else {
									products = products + ',' + (item1.modelNumber || item1.productSpec || '');
								}
								item1.selectedNum = 0
							});
						}

						// 处理图标路径
						if (item.tableHeader) {
							if (item.tableHeader.startsWith('http')) {
								// 已经是完整URL
							} else if (item.tableHeader.startsWith('static/')) {
								item.tableHeader = config.apiServer + item.tableHeader;
							}
						}

						item.selectedNum = 0;
						item.products = products;
						// 添加仓库标识，确保数据来源清晰
						item.fromWarehouse = this.employeeWarehouse?.warehouseId;
						// 添加展开状态属性
						item.isExpanded = false;
						this.navLeft.push(item);
					})
					console.log('最终的navLeft数据:', this.navLeft)
				}

				// 处理申请记录
				this.processApplications(res.data.applications || []);
			}
		},
		handleReceive(productItem, _item, type) {
			if (productItem.todo) {
				const data = productItem.todo
				data.product = productItem.todo.products[0].product
				this.receiveBtn(productItem.todo, type)
			}
		},
		// 确认领用
		receiveBtn(item, type) {
			this.query = {
				_id: item._id,
				planId: item.planId,
				employee: item.employee,
				type,
				data: item,
				product: item.product,
				productionDate: '',
			}
			console.log(item, 'item')

			if (type === 'receive') {
				// 检查是否需要生产日期
				if (this.needProductionDateForReceive(item)) {
					// 需要生产日期，显示生产日期选择界面
					this.showProductionDateDialog(item);
					return;
				}

				this.confirmContent = '是否领取'
				if (this.userInfo.branch === 'wh') { // 万华分支直接选择物品，不需要选择类别
					this.selectType({
						value: 1
					})
					return
				}
				this.menuShow = true;
			} else if (type === 'reject') {
				this.confirmContent = '是否要拒绝领取'
				this.confirmShow = true;
			}
		},

		// 签字
		signClick() {
			console.log(this.query, 'this.query');
			const that = this
			this.confirmShow = false;

			// 构建产品参数
			let productsParams = [];
			if (that.query.data && that.query.data.products) {
				productsParams = that.query.data.products.map(product => {
					// 🔑 关键修复：区分配发标准和主动申请
					let productId = null;
					let modelNumber = product.modelNumber || product.product;
					let productSpec = product.selectProductSpec || product.productSpec || '';

					// 1. 优先使用用户选择的产品（配发标准流程）
					if (product.selectedProduct) {
						productId = product.selectedProduct._id;
						modelNumber = product.selectedProduct.modelNumber || product.selectedProduct.product;
						productSpec = product.selectedProduct.productSpec || '';
						console.log('使用用户选择的产品:', productId);
					}
					// 2. 其次使用selectData（产品选择流程）
					else if (product.selectData) {
						productId = product.selectData;
						console.log('使用selectData:', productId);
					}
					// 3. 检查是否有productIds数组（主动申请或配发标准中的产品）
					else if (product.productIds && product.productIds.length > 0) {
						productId = product.productIds[0]; // 使用第一个产品ID
						console.log('使用productIds中的第一个:', productId);
					}
					// 4. 最后才使用productId字段
					else if (product.productId) {
						productId = product.productId;
						console.log('使用productId字段:', productId);
					}
					// 5. 如果都没有，设为null让后端智能匹配
					else {
						productId = null;
						console.log('产品ID为空，使用智能匹配:', {
							product: product.product,
							modelNumber: modelNumber,
							recordId: product._id
						});
					}

					// 验证必要字段
					if (!product.product) {
						console.error('产品名称缺失:', product);
						throw new Error('产品名称不能为空');
					}

					// 对于主动申请的产品，productId可以为null，后端会智能匹配
					if (!productId) {
						console.log('产品ID为空，将使用后端智能匹配:', {
							product: product.product,
							modelNumber: modelNumber,
							isManualApplication: !product.productIds || product.productIds.length === 0
						});
					}

					console.log('构建产品参数:', {
						原始product: product,
						最终productId: productId,
						最终modelNumber: modelNumber,
						最终productSpec: productSpec,
						验证通过: true
					});

					return {
						productId: productId,
						product: product.product,
						modelNumber: modelNumber,
						productSpec: productSpec,
						number: product.receiveNum || product.number || 1,
						receiveNum: product.receiveNum || product.number || 1,
						productsOrder: product._id,
						categoryId: product.categoryId || (product.standardInfo && product.standardInfo.categoryId),
						categoryPath: product.categoryPath || (product.standardInfo && product.standardInfo.categoryPath),
						categoryName: product.categoryName || (product.standardInfo && product.standardInfo.categoryName),
					};
				});
			} else {
				console.error('领用记录数据不完整:', that.query);
				uni.showToast({
					title: '数据异常，请重新进入页面',
					icon: 'error'
				});
				return;
			}

			// 验证是否所有产品都已选择
			const unselectedProducts = productsParams.filter(p => !p.productId || p.productId === p.productsOrder);
			if (unselectedProducts.length > 0) {
				uni.showToast({
					title: '请先选择具体的产品规格',
					icon: 'error',
					duration: 3000
				});
				return;
			}

			console.log('最终传递的产品参数:', productsParams);
			const paramsStr = encodeURIComponent(JSON.stringify(productsParams));

			uni.navigateTo({
				url: `./ppeSign?_id=${that.query._id}&type=${that.query.type}&planId=${that.query.planId}&employee=${that.query.employee}&claimType=${that.query.claimType}&product=${that.query.product}&productionDate=${that.query.productionDate}&params=${paramsStr}`,
			})
		},
		toggleExpand(item) {
			// 使用 Vue.set 确保响应性
			this.$set(item, 'isExpanded', !item.isExpanded);
			console.log(item, 'item')
		},
		applyTypeSelect(e) {
			this.formData.claimType = e.value
			this.formData.claimTypeLabel = e.name
		},

		confirmDate(e) {
			if (!e.value) {
				this.$refs.uToast.show({
					type: "error",
					icon: false,
					message: "请选择生产日期",
				})
				return
			}

			const date = new Date(e.value);
			const formattedDate = moment(date).format('YYYY-MM-DD');

			// 验证生产日期不能晚于当前日期
			if (date > new Date()) {
				this.$refs.uToast.show({
					type: "error",
					icon: false,
					message: "生产日期不能晚于当前日期",
				})
				return
			}

			// 更新当前产品项的生产日期
			if (this.currentProductItem) {
				this.currentProductItem.productionDate = formattedDate;
			}

			// 处理领用记录的生产日期
			if (this.currentReceiveRecord) {
				this.query.productionDate = formattedDate;
				// 继续领用流程
				this.continueReceiveProcess();
			}

			// 兼容旧的逻辑
			this.productsList.forEach(item => {
				if (item.name === '头部防护/安全帽') {
					item.productionDate = formattedDate;
				}
			})

			this.showPicker = false;
		},

		// 继续领用流程（在选择生产日期后）
		continueReceiveProcess() {
			this.confirmContent = '是否领取'
			if (this.userInfo.branch === 'wh') { // 万华分支直接选择物品，不需要选择类别
				this.selectType({
					value: 1
				})
				return
			}
			this.menuShow = true;
			// 清空当前领用记录引用
			this.currentReceiveRecord = null;
		},

		formatProductionDate(date) {
			if (!date) return '';
			// 如果已经是字符串格式，直接返回
			if (typeof date === 'string') return date;
			// 如果是Date对象，格式化为YYYY-MM-DD
			return moment(date).format('YYYY-MM-DD');
		},

		// 检查产品是否需要记录生产日期
		needProductionDate(productItem) {
			// 获取选中的具体产品
			const selectedProduct = this.getSelectedProduct(productItem);
			return selectedProduct && selectedProduct.needProductionDate;
		},

		// 获取选中的具体产品
		getSelectedProduct(productItem) {
			if (!productItem.children || !Array.isArray(productItem.children)) return null;

			// 如果有选择的产品ID，优先使用
			if (productItem.selectData) {
				const found = productItem.children.find(child => child._id === productItem.selectData);
				if (found) return found;
			}

			// 如果没有选择或找不到，且只有一个产品，返回第一个
			if (productItem.children.length === 1) {
				return productItem.children[0];
			}

			return null;
		},

		// 获取产品名称
		getProductName(productItem) {
			const selectedProduct = this.getSelectedProduct(productItem);
			return selectedProduct ? selectedProduct.product : productItem.name;
		},

		// 获取产品有效期信息
		getProductExpiryInfo(productItem) {
			const selectedProduct = this.getSelectedProduct(productItem);
			if (!selectedProduct || !selectedProduct.hasExpiry) return null;

			const period = selectedProduct.expiryPeriod;
			const unit = selectedProduct.expiryUnit;
			const unitText = {
				'days': '天',
				'months': '个月',
				'years': '年'
			}[unit] || '天';

			return `有效期${period}${unitText}`;
		},

		// 计算到期日期
		calculateExpiryDate(productItem) {
			if (!productItem.productionDate) return '';

			const selectedProduct = this.getSelectedProduct(productItem);
			if (!selectedProduct || !selectedProduct.hasExpiry) return '';

			const productionDate = moment(productItem.productionDate);
			const expiryDate = productionDate.add(selectedProduct.expiryPeriod, selectedProduct.expiryUnit);
			return expiryDate.format('YYYY-MM-DD');
		},

		// 切换帮助信息显示
		toggleHelp(productItem) {
			this.$set(productItem, 'showHelp', !productItem.showHelp);
		},

		// 打开日期选择器
		openDatePicker(productItem) {
			this.currentProductItem = productItem;
			this.showPicker = true;
		},

		// 检查领用记录是否需要生产日期
		needProductionDateForReceive(receiveRecord) {
			if (!receiveRecord.products || !receiveRecord.products.length) return false;

			const product = receiveRecord.products[0];
			// 通过产品名称和型号在分类数据中查找产品信息
			for (let category of this.navLeft) {
				if (category.data && Array.isArray(category.data)) {
					const foundProduct = category.data.find(p =>
						p.product === product.product &&
						p.modelNumber === product.modelNumber
					);
					if (foundProduct && foundProduct.needProductionDate) {
						return true;
					}
				}
			}
			return false;
		},

		// 显示生产日期选择对话框
		showProductionDateDialog(receiveRecord) {
			const product = receiveRecord.products[0];
			const productName = product.product;

			// 获取产品的有效期信息
			let expiryInfo = '';
			for (let category of this.navLeft) {
				if (category.data && Array.isArray(category.data)) {
					const foundProduct = category.data.find(p =>
						p.product === product.product &&
						p.modelNumber === product.modelNumber
					);
					if (foundProduct && foundProduct.hasExpiry) {
						const period = foundProduct.expiryPeriod;
						const unit = foundProduct.expiryUnit;
						const unitText = {
							'days': '天',
							'months': '个月',
							'years': '年'
						}[unit] || '天';
						expiryInfo = `\n有效期：${period}${unitText}`;
					}
					break;
				}
			}

			uni.showModal({
				title: '需要填写生产日期',
				content: `${productName}需要记录生产日期${expiryInfo}`,
				confirmText: '选择日期',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 设置当前处理的领用记录
						this.currentReceiveRecord = receiveRecord;
						this.showPicker = true;
					}
				}
			});
		}
	}
}
	
</script>

<style>
	.grace-page-body {
		height: 100%;
		background-color: #F6F6F6;
		position: relative;
	}

	.u-tabs {
		margin-bottom: .6rem;
	}

	.u-button {
		margin-right: 1rem;
	}
</style>
<style scoped lang="scss">
	// 简化的空状态样式
	.empty-state-simple {
		text-align: center;
		padding: 3rem 2rem;

		.empty-icon {
			font-size: 3rem;
			margin-bottom: 1rem;
		}

		.empty-text {
			color: #999;
			font-size: 1rem;
		}
	}

	// 领用
	.receiveBox {
		background-color: #ffffff;
		padding: 1rem 1rem;
		border-radius: .5rem;
		display: flex;
		justify-content: flex-start;
		flex-direction: column;

		.contain {
			display: flex;
			justify-content: flex-start;
			gap: 1rem;

			.mainBox {
				width: 50%;

				.recordTitle {
					font-weight: 600;
					color: #555555;
					margin-bottom: .4rem;
				}

				.type {
					font-size: 12px;
					height: 1.6rem;
					line-height: 1.6rem;
					font-weight: 600;
					text-align: center;
					margin-bottom: .5rem;
				}

				.under {
					background: #e1f3d8;
					color: #67c23a;
				}

				.online {
					color: #1678ff;
					background: #daecff;
				}

				.no {
					background: rgba(0, 0, 0, 0.10);
					color: #aaaaaa;
				}

				.pass {
					background: #ffe4cb;
					color: #f97d0b;
				}

				.content {
					color: #bcbcbc;
				}
			}

			.btnBar {
				// width: 50%;
				display: flex;
				align-items: center;
				flex-wrap: nowrap; // 默认不换行
				justify-content: flex-start;

				:deep(.u-button) {
					min-width: 60px; // 按钮最小宽度
				}
			}

			.timeBox {
				color: #F46C6C;
			}
		}
	}

	.receiveBoxTitle {
		margin-bottom: 20px;
		color: #2b85e4;
	}


	.timeBox {
		color: #F46C6C;
	}

	// 申请的样式
	.center {
		width: 100%;

		.list {
			display: flex;

			.center-body {
				height: 84vh;
				background-color: #ffffff;
				overflow: auto;
				flex: 1;

				.list-box {
					display: flex;
					// justify-content: space-between;
					justify-content: flex-start;
					margin: .5rem .8rem .5rem;
					padding-right: .8rem;
					box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
				}

				.main-box {
					display: flex;
					height: 5rem;
					width: 70%;

					.icon-box {
						width: 5rem;
						height: 5rem;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 1.5rem;
						border-radius: 8px;
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						font-size: 2rem;
					}

					.text-box {
						width: 40%;
						padding-left: 1rem;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						padding: .4rem;
						width: 10rem;
						line-height: 4rem;

						.title {
							color: #303133;
							font-weight: 600;
						}
					}

					.arrow-icon {
						position: absolute;
						right: 10px;
						top: 50%;
						transform: translateY(-50%);
					}
				}

				.select-box {
					width: 30%;
					height: 5rem;
					line-height: 5rem;

					.minus {
						width: 1.2rem;
						height: 1.2rem;
						background-color: #3E73FE;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
					}

					.input {
						padding: 0 10px;
					}

					.plus {
						width: 1.2rem;
						height: 1.2rem;
						background-color: #3E73FE;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
			}
		}

		.applicationBox {
			background-color: #ffffff;
			padding: 1rem 1.5rem;
			margin: 1rem;
			border-radius: .5rem;
			display: flex;
			justify-content: space-between;

			.mainBox {
				flex: 1;

				.recordTitle {
					font-weight: 600;
					color: #555555;
					margin-bottom: .5rem;
				}

				.note {
					line-height: 1.5rem;
				}

				.type {
					width: 4rem;
					height: 1.6rem;
					line-height: 1.6rem;
					font-weight: 600;
					text-align: center;
					margin: .3rem 0;
				}

				.under {
					background: #e1f3d8;
					color: #67c23a;
				}

				.no {
					background: rgba(0, 0, 0, 0.10);
					color: #aaaaaa;
				}

				.pass {
					background: #ffe4cb;
					color: #f97d0b;
				}

				.content {
					line-height: 1.5rem;
					font-size: 1rem;
					font-weight: 400;
					color: #bcbcbc;
					margin-bottom: .7rem;
				}
			}

		}
	}

	// 底部确认申请的样式
	.bottom-confirm {
		width: 100%;
		min-height: 5rem;
		box-shadow: 0px -2px 20px rgba(0, 0, 0, 0.1);
		background: linear-gradient(to top, #ffffff 0%, #ffffff 80%, rgba(255, 255, 255, 0.95) 100%);
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 999;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		padding-bottom: calc(1rem + env(safe-area-inset-bottom));
		backdrop-filter: blur(10px);

		.bottom-text {
			font-size: 1rem;
			font-weight: 500;
			line-height: 1.2;
			color: #333;
			flex: 1;
			margin-right: 1rem;

			// 添加动画效果
			transition: all 0.3s ease;

			// 当有选择时的样式
			&.has-selection {
				color: #667eea;
				font-weight: 600;
			}

			.selected-summary {
				margin-top: 4px;

				.summary-text {
					font-size: 0.8rem;
					color: #999;
					font-weight: 400;
				}
			}
		}

		.bottom-btn {
			flex-shrink: 0;
			min-width: 120px;

			:deep(.u-button) {
				border-radius: 25px !important;
				height: 44px !important;
				font-weight: 600 !important;
				transition: all 0.3s ease !important;
				min-width: 120px !important;

				&:not(.u-button--disabled) {
					box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

					&:active {
						transform: translateY(1px);
						box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
					}
				}
			}
		}
	}

	// 记录的样式
	.recordBox {
		background-color: #ffffff;
		padding: 1rem 1.5rem;
		border-radius: .5rem;

		.recordTitle {
			font-weight: 600;
			color: #555555;
			margin-bottom: .4rem;
		}

		.type {
			width: 4.2rem;
			height: 1.6rem;
			line-height: 1.6rem;
			font-weight: 600;
			text-align: center;
			margin-bottom: .3rem;
		}

		.online {
			color: #1678ff;
			background: #daecff;
		}

		.under {
			background: #e1f3d8;
			color: #67c23a;
		}

		.content {
			line-height: 1.4rem;
			font-size: 1rem;
			font-weight: 400;
			color: #bcbcbc;
			margin-bottom: .3rem;
		}

		.bottomBar {
			display: flex;
			justify-content: space-between;

			.status {
				font-size: .8rem;
				font-weight: 400;
				color: #555555;
				line-height: .8rem;

				.circle {
					width: .4rem;
					height: .4rem;
					border-radius: 50%;
					display: inline-block;
					margin-right: .2rem;
					/* 调整小圆点与文本之间的间距 */
					margin-bottom: .1rem;
				}

				.bGreen {
					background-color: #67C23A;
				}

				.bGray {
					background-color: #BCBCBC;
				}

				.bRed {
					background-color: #E02020;
				}
			}

			.time {
				font-size: .8rem;
				font-weight: 400;
				line-height: .8rem;
				color: #bcbcbc;
			}
		}
	}

	.selectProductTitle {
		text-align: center;
		padding: 10px;
		font-weight: bold;
	}

	.selectProductBody {
		height: 450px;
		overflow: scroll;
		// border: 1px solid red;
		padding: 10px;
	}

	::v-deep .u-radio {
		margin: 10px 0 !important;
	}

	.plan {
		padding: 10px;
		border: 1px solid #c8c9cc;

		.planItem {
			padding: 10px;
			display: flex;
			justify-content: space-between;
		}

		.planButton {
			display: inline-block;
			margin-right: 10px;
		}
	}



	// 添加媒体查询，处理超小屏幕
	@media screen and (max-width: 375px) {
		.receiveBox {
			.contain {
				flex-direction: column; // 在超小屏幕上改为垂直布局

				.btnBar {
					justify-content: flex-start;
					margin-top: 0.5rem;
				}
			}
		}
	}

	.list-box {
		position: relative;

		.expand-icon {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
		}
	}

	.sub-list {
		background: #f8f8f8;
		margin: 0 0.8rem;
		border-radius: 4px;

		.sub-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #eee;

			&:last-child {
				border-bottom: none;
			}

			.text-box {
				width: 50%;
				text-align: center;
			}

			.select-box {
				height: 5rem;
				line-height: 5rem;

				.minus {
					width: 1.2rem;
					height: 1.2rem;
					background-color: #3E73FE;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.input {
					padding: 0 10px;
				}

				.plus {
					width: 1.2rem;
					height: 1.2rem;
					background-color: #3E73FE;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}

	.date-picker-box {
		margin: 10px 0;
		padding: 10px;
		background: #fff;
		border-radius: 4px;

		.date-picker-title {
			font-size: 14px;
			color: #666;
			margin-bottom: 8px;
		}

		.date-picker-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 8px;
			background: #f8f8f8;
			border-radius: 4px;

			text {
				color: #333;
			}
		}
	}

	.product-section {
		background: #ffffff;
		border-radius: 8px;
		padding: 16px;
		margin: 0 16px 16px 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		border: 1px solid #e9ecef;
	}

	.product-title {
		font-size: 16px;
		font-weight: 600;
		color: #495057;
		margin-bottom: 12px;
		padding-bottom: 8px;
		border-bottom: 1px solid #dee2e6;
	}

	.date-section {
		background-color: #f8f8ff; // 使用略微不同的背景色区分
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 20px;
		margin-top: 5px;
		border: 1px solid #e2e6ff;

		.date-section-title-container {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
		}

		.date-section-title {
			font-size: 15px;
			font-weight: 500;
			color: #2979ff;
			margin-bottom: 0;
		}

		.help-icon {
			margin-left: 5px;
			display: flex;
			align-items: center;
		}

		.date-tip {
			font-size: 12px;
			color: #ff9800;
			margin-bottom: 10px;
			padding-left: 2px;
		}

		.date-selector {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #ffffff;
			padding: 10px 15px;
			border-radius: 6px;
			border: 1px solid #e1e4f3;

			.date-text {
				color: #333;
				font-size: 14px;
			}
		}
	}

	// 为selectProductBody添加一些改进
	.selectProductBody {
		height: 450px;
		overflow-y: auto;
		padding: 15px;

		// 添加底部空间以确保在滚动时有足够空间查看所有内容
		padding-bottom: 30px;
	}

	.tabs-container {
		position: relative;
		width: 100% !important;
		height: 100%;
		overflow: hidden;
	}
	
	.tab-content {
		width: 100%;
		height: calc(100% - 2.6rem); /* 减去tabs的高度 */
		overflow-y: auto;
		background-color: #F6F6F6;
		position: relative; /* 确保定位上下文 */
		z-index: 1; /* 确保正常z-index堆叠 */
		padding-bottom: 6rem; /* 添加底部内边距，为固定的bottom-confirm腾出空间 */
	}
	
	.tab-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #F6F6F6;
	}

	/* 新的卡片式设计 */
	.category-container {
		padding: 16px;
		background-color: #f5f7fa;
		min-height: 100vh;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		background: #ffffff;
		border-radius: 12px;
		margin-bottom: 16px;

		.empty-icon {
			font-size: 4rem;
			margin-bottom: 1rem;
		}

		.empty-text {
			color: #333;
			font-size: 1.2rem;
			font-weight: 500;
			margin-bottom: 0.5rem;
		}

		.empty-subtitle {
			color: #999;
			font-size: 0.9rem;
		}
	}

	.category-card {
		background: #ffffff;
		border-radius: 12px;
		margin-bottom: 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.category-card:hover {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
	}

	.category-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px;
		border-bottom: 1px solid #f0f2f5;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.category-header:active {
		background-color: #f8f9fa;
	}

	.category-info {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.category-icon {
		width: 48px;
		height: 48px;
		border-radius: 12px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16px;
		position: relative;
		overflow: hidden;

		// 分类渐变背景
		&[data-category="head"] {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		}
		&[data-category="eye"] {
			background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		}
		&[data-category="respiratory"] {
			background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		}
		&[data-category="hand"] {
			background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
		}
		&[data-category="foot"] {
			background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
		}
		&[data-category="body"] {
			background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
		}
		&[data-category="fall"] {
			background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
		}
		&[data-category="default"] {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		}

		.category-letter {
			font-size: 16px;
			font-weight: bold;
			color: white;
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
		}
	}

	.category-details {
		flex: 1;
	}

	.category-title {
		font-size: 18px;
		font-weight: 600;
		color: #1a1a1a;
		margin-bottom: 4px;
	}

	.category-subtitle {
		font-size: 13px;
		color: #8c8c8c;
		margin-bottom: 4px;
	}

	.category-count {
		font-size: 12px;
		color: #409EFF;
		background: #e6f4ff;
		padding: 2px 8px;
		border-radius: 10px;
		display: inline-block;
	}

	.expand-button {
		padding: 8px;
		border-radius: 6px;
		transition: background-color 0.2s ease;

		.expand-icon {
			font-size: 14px;
			color: #409EFF;
			font-weight: bold;
		}
	}

	.expand-button:active {
		background-color: #f0f0f0;
	}

	.products-grid {
		padding: 0 20px 20px;
	}

	.product-card {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px;
		margin-bottom: 12px;
		background: #fafbfc;
		border-radius: 8px;
		border: 1px solid #e8eaed;
		transition: all 0.3s ease;
		position: relative;

		&.selected {
			border-color: #667eea;
			background: linear-gradient(135deg, #f6f9ff 0%, #f0f4ff 100%);
			box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 3px;
				height: 100%;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				border-radius: 0 0 0 8px;
			}
		}

		&.low-stock {
			border-color: #ff9800;
			background: #fff8f0;
		}
	}

	.product-card:last-child {
		margin-bottom: 0;
	}

	.product-card:hover {
		border-color: #409EFF;
		background: #f6f9ff;
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
	}

	.product-info {
		flex: 1;
	}

	.product-title {
		font-size: 16px;
		font-weight: 500;
		color: #333;
		margin-bottom: 6px;
	}

	.product-spec {
		font-size: 13px;
		color: #666;
		margin-bottom: 4px;
	}

	.product-stock {
		font-size: 12px;
		color: #52c41a;
		font-weight: 500;
	}

	.product-stock.low-stock {
		color: #ff4d4f;
	}

	.product-actions {
		margin-left: 16px;
	}

	.quantity-selector {
		display: flex;
		align-items: center;
		background: #f0f2f5;
		border-radius: 20px;
		padding: 2px;
		transition: all 0.3s ease;

		&.active {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

			.quantity-display {
				color: white;
				font-weight: 600;
			}
		}
	}

	.quantity-btn {
		width: 32px;
		height: 32px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #409EFF;
		cursor: pointer;
		transition: all 0.2s ease;

		&:active {
			transform: scale(0.95);
		}

		&.disabled {
			background: #d9d9d9;
			cursor: not-allowed;
			opacity: 0.6;
		}
	}

	.quantity-btn.minus {
		margin-right: 2px;
	}

	.quantity-btn.plus {
		margin-left: 2px;
	}

	.quantity-display {
		min-width: 40px;
		text-align: center;
		font-size: 16px;
		font-weight: 600;
		color: #333;
		padding: 0 8px;
		transition: all 0.3s ease;
	}

	/* 响应式设计 */
	@media (max-width: 480px) {
		.category-container {
			padding: 12px;
		}

		.category-header {
			padding: 16px;
		}

		.category-title {
			font-size: 16px;
		}

		.product-card {
			padding: 12px;
		}

		.product-title {
			font-size: 14px;
		}
	}

	/* 动画效果 */
	.products-grid {
		animation: slideDown 0.3s ease-out;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 空状态样式 */
	.empty-state {
		text-align: center;
		padding: 40px 20px;
		color: #999;
	}

	/* 生产日期相关样式 - 简洁版本 */
	.date-section {
		margin-top: 16px;
		padding: 16px;
		background: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #e9ecef;
	}

	.date-section-title-container {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}

	.date-section-title {
		font-size: 14px;
		font-weight: 600;
		color: #495057;
		margin-right: 8px;
	}

	.help-icon {
		cursor: pointer;
		padding: 2px;
	}

	.date-tip {
		font-size: 12px;
		color: #6c757d;
		margin-bottom: 12px;
		padding: 8px 12px;
		background: #e3f2fd;
		border-radius: 4px;
		border-left: 3px solid #2196f3;
	}

	.date-selector {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px;
		background: white;
		border: 1px solid #ced4da;
		border-radius: 6px;
		cursor: pointer;
		transition: border-color 0.2s ease;
	}

	.date-selector:hover {
		border-color: #80bdff;
	}

	.date-text {
		font-size: 14px;
		color: #495057;
	}

	.expiry-date-display {
		margin-top: 12px;
		padding: 8px 12px;
		background: #e8f5e8;
		border-radius: 6px;
		border-left: 3px solid #28a745;
	}

	.expiry-label {
		font-size: 12px;
		color: #6c757d;
	}

	.expiry-date {
		font-size: 14px;
		font-weight: 600;
		color: #28a745;
		margin-left: 4px;
	}

	.empty-state .empty-icon {
		font-size: 48px;
		margin-bottom: 16px;
		opacity: 0.5;
	}

	.empty-state .empty-text {
		font-size: 14px;
		line-height: 1.5;
	}
</style>