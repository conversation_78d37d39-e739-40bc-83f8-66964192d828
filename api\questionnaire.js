"use strict";
import req from "@/utils/http.js"; //导入 封装的请求

export default {
  // 获取个人问卷列表
  getPersonalQuestionnaire: (data) => {
    return req({
      url: "manage/questionnaire/getPersonalQuestionnaire",
      method: "post",
      data,
    });
  },

  // 获取个人指定问卷列表
  getPersonalQuestionnaireById: (data) => {
    return req({
      url: "manage/questionnaire/getPersonalQuestionnaireById",
      method: "post",
      data,
    });
  },

  // 提交答卷
  submitSurvey: (data) => {
    return req({
      url: "manage/questionnaire/submitSurvey",
      method: "post",
      data,
    });
  },

  // 扫码获取问卷
  getQuestionnaireByCode: (data) => {
    return req({
      url: "manage/questionnaire/getQuestionnaireByCode",
      method: "post",
      data,
    });
  },
};
