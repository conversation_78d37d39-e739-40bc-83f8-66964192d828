(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-addItems"],{"0af7":function(t,e,a){"use strict";var n=a("eab1"),i=a.n(n);i.a},"279a":function(t,e,a){"use strict";a.r(e);var n=a("3ff1"),i=a("a7d6");for(var c in i)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(c);a("0af7");var o=a("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"656778c8",null,!1,n["a"],void 0);e["default"]=r.exports},"2b54":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'.grace-check-btn[data-v-37a06640]{color:#999;flex-shrink:0}.grace-check-btn[data-v-37a06640]:after{content:"\\e762"}.grace-check-checked[data-v-37a06640]:after{content:"\\e7f8"}.grace-check-lable[data-v-37a06640]{color:#555;margin-left:%?20?%;font-size:%?26?%;width:%?700?%}',""]),t.exports=e},"2b7e":function(t,e,a){"use strict";(function(t){a("7a82");var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d3b7"),a("13d5"),a("7db0"),a("ac1f");var i=n(a("5530")),c=n(a("e866")),o=(n(a("d3d9")),a("26cb")),r={name:"AddItems",components:{graceCheckBtn:c.default},data:function(){return{list:[],tabCur:0,mainCur:0,verticalNavTop:0,load:!0}},computed:(0,i.default)((0,i.default)({},(0,o.mapGetters)(["optionalItems"])),{},{optionalItemsSelected:function(){return this.optionalItems.filter((function(t){return t.checked}))},sumPrice:function(){return this.optionalItemsSelected.reduce((function(t,e){return t+e.price}),0).toFixed(2)}}),methods:{checkedChange:function(t){t.checked=!t.checked,"胃肠镜检查（普通肠镜）"===t.name?this.optionalItems.find((function(t){return"胃肠镜检查（无痛肠镜）"===t.name})).checked=!1:"胃肠镜检查（无痛肠镜）"===t.name?this.optionalItems.find((function(t){return"胃肠镜检查（普通肠镜）"===t.name})).checked=!1:"胃肠镜检查（普通胃镜）"===t.name?this.optionalItems.find((function(t){return"胃肠镜检查（无痛胃镜）"===t.name})).checked=!1:"胃肠镜检查（无痛胃镜）"===t.name&&(this.optionalItems.find((function(t){return"胃肠镜检查（普通胃镜）"===t.name})).checked=!1)},TabSelect:function(t){this.tabCur=t.currentTarget.dataset.id,this.mainCur=t.currentTarget.dataset.id,this.verticalNavTop=50*(t.currentTarget.dataset.id-1)},VerticalMain:function(t){var e=this;this.load&&function(){for(var t=0,a=function(a){var n=uni.createSelectorQuery().select("#main-"+e.list[a].id);n.fields({size:!0},(function(n){n&&(e.list[a].top=t,t+=n.height,e.list[a].bottom=t)})).exec()},n=0;n<e.list.length;n++)a(n);e.load=!1}();for(var a=t.detail.scrollTop+10,n=0;n<this.list.length;n++)if(a>this.list[n].top&&a<this.list[n].bottom)return this.verticalNavTop=50*(this.list[n].id-1),this.tabCur=this.list[n].id,!1},back:function(){uni.navigateBack(),this.$store.commit("setOptionalItemsSelected",this.optionalItemsSelected)}},onLoad:function(){uni.showLoading({title:"加载中...",mask:!0});for(var e=[{}],a=0;a<26;a++)e[a]={},e[a].name=String.fromCharCode(65+a),e[a].id=a;this.list=e,this.listCur=e[0],t("log",this.list," at pages_user/pages/user/addItems.vue:163")},onReady:function(){uni.hideLoading()}};e.default=r}).call(this,a("0de9")["log"])},"3ff1":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return c})),a.d(e,"a",(function(){return n}));var n={gracePage:a("c14d").default,graceCheckBtn:a("e866").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"添加加项"},slot:"gHeader"}),a("v-uni-view",{attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"VerticalBox"},[a("v-uni-scroll-view",{staticClass:"VerticalNav nav",attrs:{"scroll-y":!0,"scroll-with-animation":!0,"scroll-top":t.verticalNavTop}},t._l(t.optionalItems,(function(e,n){return a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.name.includes("胃肠镜检查"),expression:"!item.name.includes('胃肠镜检查')"}],key:n,staticClass:"cu-item",class:(n==t.tabCur?"cur":"")+(e.checked?" checked":""),attrs:{"data-id":n},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.TabSelect.apply(void 0,arguments)}}},[a("v-uni-view",{staticStyle:{margin:"0 24rpx"}},[a("v-uni-text",[t._v(t._s(e.name))])],1)],1)})),1),a("v-uni-scroll-view",{staticClass:"VerticalMain",attrs:{"scroll-y":!0,"scroll-with-animation":!0,"scroll-into-view":"main-"+t.mainCur},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.VerticalMain.apply(void 0,arguments)}}},t._l(t.optionalItems,(function(e,n){return a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.name.includes("胃肠镜检查"),expression:"!item.name.includes('胃肠镜检查')"}],key:n,staticClass:"checkItem",attrs:{id:"main-"+n}},[a("v-uni-view",{staticClass:"itemsCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titleName"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"titlePrice"},[a("v-uni-text",{staticStyle:{"font-size":"12rpx"}},[t._v("¥")]),a("v-uni-text",{staticStyle:{"font-size":"28rpx"}},[t._v(t._s(e.price))]),a("v-uni-view",{staticStyle:{width:"36rpx"}},[a("graceCheckBtn",{attrs:{size:36,checkedColor:"#409EFF",checked:e.checked},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.checkedChange(e)}}})],1)],1)],1),a("v-uni-view",{staticClass:"splitLine"}),t._l(e.comments,(function(e,n){return a("v-uni-view",{key:n,staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1)],1)})),0===e.comments.length?a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v("暂无项目详情")])],1)],1):t._e()],2)],1)})),1)],1)],1),a("v-uni-view",{staticClass:"grace-footer grace-nowrap grace-box-shadow",attrs:{slot:"gFooter"},slot:"gFooter"},[a("v-uni-view",{staticClass:"totalCount"},[a("v-uni-view",{staticStyle:{display:"flex","flex-direction":"column","align-items":"flex-end"}},[a("v-uni-view",{staticStyle:{display:"flex"}},[a("v-uni-text",{staticStyle:{color:"#606266"}},[t._v("共"+t._s(t.optionalItemsSelected.length)+"项检查")]),a("v-uni-text",{staticStyle:{"margin-left":"24rpx"}},[t._v("合计")]),a("v-uni-text",{staticStyle:{"font-size":"24rpx",color:"#EA5C3F"}},[t._v("￥")]),a("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#EA5C3F"}},[t._v(t._s(t.sumPrice))])],1)],1),a("v-uni-view",{staticStyle:{"margin-left":"37rpx"}},[a("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1)],1)},c=[]},"43e2":function(t,e,a){"use strict";a.r(e);var n=a("5aa9"),i=a.n(n);for(var c in n)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(c);e["default"]=i.a},"4aeb":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"grace-nowrap grace-flex-vcenter",style:{width:t.width},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.changeStatus.apply(void 0,arguments)}}},[a("v-uni-view",{class:["grace-check-btn grace-icons",t.status?"grace-check-checked":""],style:{fontSize:t.size+"rpx",lineHeight:t.size+"rpx",color:t.status?t.checkedColor:t.color}}),a("v-uni-view",{staticClass:"grace-check-lable"},[t._t("default")],2)],1)},i=[]},"5aa9":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n={props:{width:{type:String,default:"100%"},size:{type:Number,default:38},color:{type:String,default:"#EEEEEE"},checked:{type:Boolean,default:!1},checkedColor:{type:String,default:"#FF0036"},parameter:{type:Array,default:function(){return[]}}},data:function(){return{status:!1}},watch:{checked:function(t,e){this.status=t}},created:function(){this.status=this.checked},methods:{changeStatus:function(){this.status=!this.status,this.$emit("change",[this.status,this.parameter])}}};e.default=n},79856:function(t,e,a){var n=a("2b54");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("b546d536",n,!0,{sourceMap:!1,shadowMode:!1})},a7d6:function(t,e,a){"use strict";a.r(e);var n=a("2b7e"),i=a.n(n);for(var c in n)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(c);e["default"]=i.a},d214:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'.fixed[data-v-656778c8]{position:fixed;z-index:99}.VerticalNav.nav[data-v-656778c8]{width:%?200?%;height:calc(100vh - %?210?%);white-space:normal}.VerticalNav.nav .cu-item[data-v-656778c8]{width:100%;text-align:center;background:#f2f2f2;margin:0;border:none;height:50px;position:relative;display:flex;justify-content:center;align-items:center}.VerticalNav.nav .cu-item uni-text[data-v-656778c8]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;text-overflow:ellipsis;word-break:break-all}.VerticalNav.nav .cu-item.cur[data-v-656778c8]{background-color:#fff}.VerticalBox[data-v-656778c8]{display:flex}.VerticalMain[data-v-656778c8]{height:calc(100vh - %?210?%);background-color:#fff;flex:1}.VerticalMain .checkItem[data-v-656778c8]{padding:0 12px;margin-top:8px}.VerticalMain .checkItem[data-v-656778c8]:last-child{padding-bottom:12.5px}.VerticalMain .checkItem .itemsCard[data-v-656778c8]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.VerticalMain .checkItem .itemsCard .cardTitle[data-v-656778c8]{margin-bottom:16px;display:flex;justify-content:space-between}.VerticalMain .checkItem .itemsCard .cardTitle .titleName[data-v-656778c8]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.VerticalMain .checkItem .itemsCard .cardTitle .titlePrice[data-v-656778c8]{color:#ea5c3f;display:flex;align-items:center}.VerticalMain .checkItem .itemsCard .cardItem[data-v-656778c8]{display:flex;margin-bottom:12px;justify-content:space-between}.VerticalMain .checkItem .itemsCard .cardItem[data-v-656778c8]:last-child{margin-bottom:0}.VerticalMain .checkItem .itemsCard .cardItem .itemLabel[data-v-656778c8]{font-weight:400;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.splitLine[data-v-656778c8]{margin-bottom:12px;width:100%;height:0;opacity:1;border-top:1px solid #e9e9e9}.totalCount[data-v-656778c8]{width:100%;height:%?66?%;padding:0 %?20?%;margin:%?20?% %?30?% %?32?% %?30?%;display:flex;flex-wrap:nowrap;align-items:center;justify-content:flex-end}.totalCount uni-button[data-v-656778c8]{color:#fff;background-color:#3e73fe;border:1px solid #3e73fe;border-radius:4px}.VerticalNav.nav .cu-item.checked[data-v-656778c8]::after{content:"";width:%?12?%;height:%?32?%;border-radius:%?6?%;position:absolute;background-color:currentColor;top:0;left:%?4?%;bottom:0;margin:auto;color:#3e73fe}',""]),t.exports=e},d3d9:function(t,e,a){"use strict";a("7a82");var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("1067")),c={getTjPlan:function(t){return(0,i.default)({url:"manage/tjAppointment/getTjPlan",method:"get",data:t})},getTjEmployee:function(t){return(0,i.default)({url:"manage/tjAppointment/getTjEmployee",method:"post",data:t})},getTjAppointment:function(t){return(0,i.default)({url:"manage/tjAppointment/getTjAppointment",method:"get",data:t})},getRequiredCheckItemList:function(t){return(0,i.default)({url:"manage/tjAppointment/getRequiredCheckItemList",method:"get",data:t})},getOccupationalHealth:function(t){return(0,i.default)({url:"manage/tjAppointment/getOccupationalHealth",method:"get",data:t})},getOptionalCheckItemList:function(t){return(0,i.default)({url:"manage/tjAppointment/getOptionalCheckItemList",method:"get",data:t})},createTjAppointment:function(t){return(0,i.default)({url:"manage/tjAppointment/createTjAppointment",method:"post",data:t})},cancelTjAppointment:function(t){return(0,i.default)({url:"manage/tjAppointment/cancelTjAppointment",method:"post",data:t})},updateTjAppointment:function(t){return(0,i.default)({url:"manage/tjAppointment/updateTjAppointment",method:"post",data:t})},getTjPlanCount:function(t){return(0,i.default)({url:"manage/tjAppointment/getTjPlanCount",method:"get",data:t})},getQuestionnaireUrl:function(t){return(0,i.default)({url:"manage/tjAppointment/getQuestionnaireUrl",method:"get",data:t})},getBranch:function(t){return(0,i.default)({url:"manage/tjAppointment/getBranch",method:"get",params:t})},checkGastrocolonoscopyAllow:function(t){return(0,i.default)({url:"manage/tjAppointment/checkGastrocolonoscopyAllow",method:"get",data:t})},getGastrocolonoscopyItem:function(t){return(0,i.default)({url:"manage/tjAppointment/getGastrocolonoscopyItem",method:"get",params:t})},getGCscopeByPlanIdEmployeeId:function(t){return(0,i.default)({url:"manage/tjAppointment/getGCscopeByPlanIdEmployeeId",method:"get",data:t})},updateGastrocolonoscopyReservationDate:function(t){return(0,i.default)({url:"manage/tjAppointment/updateGastrocolonoscopyReservationDate",method:"post",data:t})}},o=c;e.default=o},e866:function(t,e,a){"use strict";a.r(e);var n=a("4aeb"),i=a("43e2");for(var c in i)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(c);a("fa7d");var o=a("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"37a06640",null,!1,n["a"],void 0);e["default"]=r.exports},eab1:function(t,e,a){var n=a("d214");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("4a21b84c",n,!0,{sourceMap:!1,shadowMode:!1})},fa7d:function(t,e,a){"use strict";var n=a("79856"),i=a.n(n);i.a}}]);