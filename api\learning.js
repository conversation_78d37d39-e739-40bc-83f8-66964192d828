// 法律法规 
"use strict";
import req from '@/utils/http.js';

export default {
  
  list: data => {
    return req({
      url: 'app/learning/list',
      method: 'post',
      data,
    });
  },
  // 获取文章类别的id, 详见后端config设置
    // informationPlatform 健康企业信息平台
    // industryNews        行业动态
    // trainingEducation   培训教育
  categories: data => {
    return req({
      url: 'app/learning/categories',
      method: 'get',
      data,
    });
  },
  // 首页中的全局搜索
  globalSearch: data => {
    return req({
      url: 'app/learning/globalSearch',
      method: 'post',
      data,
    });
  },
  // 获取公开课列表
	publicCoursesList: data => {
		return req({
			url: 'app/learning/publicCoursesList',
			method: 'post',
			data
		})
	},
  
};


